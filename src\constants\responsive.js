/*
 * 响应式设计常量
 * 统一管理所有响应式断点和相关配置
 */

// 响应式断点定义 (与 Tailwind CSS 保持一致)
export const BREAKPOINTS = {
  XS: 480,    // 超小屏幕 (手机)
  SM: 576,    // 小屏幕 (大手机)
  MD: 768,    // 中等屏幕 (平板)
  LG: 992,    // 大屏幕 (小桌面)
  XL: 1200,   // 超大屏幕 (桌面)
  XXL: 1400,  // 超超大屏幕 (大桌面)
  XXXL: 1600, // 超宽屏
}

// 设备类型枚举
export const DEVICE_TYPES = {
  MOBILE: 'mobile',
  TABLET: 'tablet',
  DESKTOP: 'desktop',
  LARGE_DESKTOP: 'large-desktop'
}

// 根据宽度判断设备类型
export const getDeviceType = (width) => {
  if (width < BREAKPOINTS.MD) return DEVICE_TYPES.MOBILE
  if (width < BREAKPOINTS.LG) return DEVICE_TYPES.TABLET
  if (width < BREAKPOINTS.XXL) return DEVICE_TYPES.DESKTOP
  return DEVICE_TYPES.LARGE_DESKTOP
}

// 媒体查询字符串生成器
export const createMediaQuery = (minWidth, maxWidth = null) => {
  if (maxWidth) {
    return `(min-width: ${minWidth}px) and (max-width: ${maxWidth - 1}px)`
  }
  return `(min-width: ${minWidth}px)`
}

// 常用媒体查询
export const MEDIA_QUERIES = {
  // 移动端优先
  XS_UP: createMediaQuery(BREAKPOINTS.XS),
  SM_UP: createMediaQuery(BREAKPOINTS.SM),
  MD_UP: createMediaQuery(BREAKPOINTS.MD),
  LG_UP: createMediaQuery(BREAKPOINTS.LG),
  XL_UP: createMediaQuery(BREAKPOINTS.XL),
  XXL_UP: createMediaQuery(BREAKPOINTS.XXL),
  
  // 仅特定范围
  XS_ONLY: createMediaQuery(0, BREAKPOINTS.SM),
  SM_ONLY: createMediaQuery(BREAKPOINTS.SM, BREAKPOINTS.MD),
  MD_ONLY: createMediaQuery(BREAKPOINTS.MD, BREAKPOINTS.LG),
  LG_ONLY: createMediaQuery(BREAKPOINTS.LG, BREAKPOINTS.XL),
  XL_ONLY: createMediaQuery(BREAKPOINTS.XL, BREAKPOINTS.XXL),
  
  // 桌面端优先
  XS_DOWN: `(max-width: ${BREAKPOINTS.SM - 1}px)`,
  SM_DOWN: `(max-width: ${BREAKPOINTS.MD - 1}px)`,
  MD_DOWN: `(max-width: ${BREAKPOINTS.LG - 1}px)`,
  LG_DOWN: `(max-width: ${BREAKPOINTS.XL - 1}px)`,
  XL_DOWN: `(max-width: ${BREAKPOINTS.XXL - 1}px)`,
}

// 响应式字体大小配置
export const RESPONSIVE_FONT_SIZES = {
  // 标题字体
  TITLE_LARGE: {
    mobile: '1.875rem',  // 30px
    tablet: '2.25rem',   // 36px
    desktop: '3rem',     // 48px
  },
  TITLE_MEDIUM: {
    mobile: '1.5rem',    // 24px
    tablet: '1.875rem',  // 30px
    desktop: '2.25rem',  // 36px
  },
  TITLE_SMALL: {
    mobile: '1.25rem',   // 20px
    tablet: '1.5rem',    // 24px
    desktop: '1.875rem', // 30px
  },
  
  // 正文字体
  BODY_LARGE: {
    mobile: '1rem',      // 16px
    tablet: '1.125rem',  // 18px
    desktop: '1.25rem',  // 20px
  },
  BODY_MEDIUM: {
    mobile: '0.875rem',  // 14px
    tablet: '1rem',      // 16px
    desktop: '1.125rem', // 18px
  },
  BODY_SMALL: {
    mobile: '0.75rem',   // 12px
    tablet: '0.875rem',  // 14px
    desktop: '1rem',     // 16px
  },
}

// 响应式间距配置
export const RESPONSIVE_SPACING = {
  SECTION_PADDING: {
    mobile: '1rem',      // 16px
    tablet: '2rem',      // 32px
    desktop: '3rem',     // 48px
  },
  CONTAINER_PADDING: {
    mobile: '1rem',      // 16px
    tablet: '1.5rem',    // 24px
    desktop: '2rem',     // 32px
  },
  ELEMENT_MARGIN: {
    mobile: '0.5rem',    // 8px
    tablet: '1rem',      // 16px
    desktop: '1.5rem',   // 24px
  },
}

// 响应式容器最大宽度
export const CONTAINER_MAX_WIDTHS = {
  SM: '540px',
  MD: '720px',
  LG: '960px',
  XL: '1140px',
  XXL: '1320px',
}

// 检查是否为移动设备
export const isMobile = (width = window.innerWidth) => {
  return width < BREAKPOINTS.LG
}

// 检查是否为平板设备
export const isTablet = (width = window.innerWidth) => {
  return width >= BREAKPOINTS.MD && width < BREAKPOINTS.LG
}

// 检查是否为桌面设备
export const isDesktop = (width = window.innerWidth) => {
  return width >= BREAKPOINTS.LG
}

// 获取当前断点
export const getCurrentBreakpoint = (width = window.innerWidth) => {
  if (width < BREAKPOINTS.SM) return 'xs'
  if (width < BREAKPOINTS.MD) return 'sm'
  if (width < BREAKPOINTS.LG) return 'md'
  if (width < BREAKPOINTS.XL) return 'lg'
  if (width < BREAKPOINTS.XXL) return 'xl'
  return '2xl'
}
