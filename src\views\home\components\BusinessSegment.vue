<template>
  <div class="business-segment-main">
    <div class="content">
      <h2 class="segment-title">{{ $t("businessSegment.title") }}</h2>
      <div class="list">
        <el-carousel height="auto" class="business-carousel" motion-blur>
          <el-carousel-item
            :style="`height: ${itemStyleHeight}px`"
            class="carousel-item-style"
            v-for="(item, index) in businessList"
            :key="index"
          >
            <div class="business-carousel-item">
              <div class="carousel-image-view">
                <el-image @load="loadImage" class="carousel-image" :src="item.img" fit="cover" />
              </div>
              <div class="txt-view" v-if="refreshBusiness">
                <div class="title">{{ item[lang].title }}</div>
                <div class="desc" v-html="item[lang].homeDesc"></div>
                <div class="more" @click="gotoBusinessDetails(item.key)">{{ $t("public.more") }}</div>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, watch, onMounted, nextTick, onBeforeUpdate } from "vue"
import { i18n } from "@/i18n"
import BusinessSegmentList from "../../business-segment/business-segment-list.js"

import { useRouter } from "vue-router"
const router = useRouter()

const refreshBusiness = ref(true)
const itemStyleHeight = ref(900) // 给个初始值 然后重新计算
const businessList = reactive(BusinessSegmentList)

const lang = ref(i18n.global.locale)

const gotoBusinessDetails = (key) => {
  const pathMap = {
    AcademyOfSciences: "/business-segment/business-details",
    GlobalEnergyValley: "/business-segment/energy-valley-details"
  }
  router.push({ path: pathMap[key] })
}

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"

    refreshBusiness.value = false

    nextTick(() => {
      refreshBusiness.value = true

      nextTick(() => {
        refreshBusinessList()
      })
    })
  }
)

const refreshBusinessList = () => {
  const businessCarouselItems = document.querySelectorAll(".business-carousel-item")
  let maxHeight = 0
  businessCarouselItems.forEach((item) => {
    maxHeight = Math.max(maxHeight, item.clientHeight)
  })
  itemStyleHeight.value = maxHeight

  loadImgNum.value = 0
}

const loadImgNum = ref(0)
const loadImage = () => {
  loadImgNum.value++
  if (loadImgNum.value === businessList.length) {
    refreshBusinessList()
  }
}

onMounted(() => {
  window.addEventListener("resize", () => {
    refreshBusinessList()
  })
})

onBeforeUpdate(() => {
  window.removeEventListener("resize", () => {})
})
</script>

<style lang="scss" scoped>
.business-segment-main {
  background: #f0f2f5;

  .content {
    @extend .responsive-container;

    // 响应式顶部间距
    padding-top: 3rem;

    @include respond-to(md) {
      padding-top: 4rem;
    }

    @include respond-to(lg) {
      padding-top: 5rem;
    }

    @include respond-to(xl) {
      padding-top: 6rem;
    }

    @include respond-to(fhd) {
      padding-top: 7rem;
    }

    @include respond-to(qhd) {
      padding-top: 8rem;
    }

    @include respond-to(uhd) {
      padding-top: 10rem;
    }

    .segment-title {
      color: rgba(0, 0, 0, 0.85);
      text-align: center;
      font-weight: 700;

      // 响应式字体大小和字间距
      font-size: 28px;
      letter-spacing: 2px;
      margin-bottom: 2rem;

      @include respond-to(sm) {
        font-size: 32px;
        letter-spacing: 3px;
        margin-bottom: 2.5rem;
      }

      @include respond-to(md) {
        font-size: 36px;
        letter-spacing: 3px;
        margin-bottom: 3rem;
      }

      @include respond-to(lg) {
        font-size: 42px;
        letter-spacing: 4px;
        margin-bottom: 3.5rem;
      }

      @include respond-to(xl) {
        font-size: 46px;
        letter-spacing: 4px;
        margin-bottom: 4rem;
      }

      @include respond-to(fhd) {
        font-size: 50px;
        letter-spacing: 5px;
        margin-bottom: 4.5rem;
      }

      @include respond-to(qhd) {
        font-size: 56px;
        letter-spacing: 6px;
        margin-bottom: 5rem;
      }

      @include respond-to(uhd) {
        font-size: 64px;
        letter-spacing: 7px;
        margin-bottom: 6rem;
      }
    }

    .list {
      // 响应式内边距
      padding-top: 2rem;
      padding-bottom: 1.5rem;

      @include respond-to(md) {
        padding-top: 2.5rem;
        padding-bottom: 2rem;
      }

      @include respond-to(lg) {
        padding-top: 3rem;
        padding-bottom: 2.5rem;
      }

      @include respond-to(fhd) {
        padding-top: 4rem;
        padding-bottom: 3rem;
      }

      @include respond-to(qhd) {
        padding-top: 5rem;
        padding-bottom: 4rem;
      }

      @include respond-to(uhd) {
        padding-top: 6rem;
        padding-bottom: 5rem;
      }

      .business-carousel {
        position: relative;

        :deep(.el-carousel__arrow--left) {
          left: 1rem;
          top: 30%;

          @include respond-to(md) {
            left: 1.5rem;
          }

          @include respond-to(lg) {
            left: 2rem;
          }

          @include respond-to(fhd) {
            left: 2.5rem;
          }
        }

        :deep(.el-carousel__arrow--right) {
          right: 20px;
          top: 30%;
        }

        :deep(.el-carousel__container) {
          height: 100%;
        }

        :deep(.el-carousel__arrow) {
          font-size: 70px;
          color: #d2d2d2;
          background: none;
        }

        :deep(.el-carousel__indicators) {
          display: none;
        }

        .business-carousel-item {
          width: 1200px;
          margin: 0 auto;
          position: relative;
          display: flex;
          flex-direction: column;
          justify-content: center;
        }

        .carousel-image {
          display: block;
          width: 100%;
          height: auto;
        }

        .txt-view {
          color: #000000;

          .title {
            font-weight: 600;
            font-size: 42px;
            color: rgba(0, 0, 0, 0.85);
            margin: 40px 0;
          }

          .desc {
            font-size: 18px;
            //letter-spacing: 1px;
            padding-top: 10px;
          }

          .more {
            margin-top: 40px;
            height: 40px;
            width: 130px;
            border-radius: 20px;
            border: 1px solid #1f2a72;
            cursor: pointer;
            font-weight: 600;
            font-size: 18px;
            color: #1f2a72;
            line-height: 40px;
            text-align: center;

            &:hover {
              background-color: #1f2a72;
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}
@import "./media.scss";
</style>
