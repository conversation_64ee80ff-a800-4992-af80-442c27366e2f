/** 自定义 Element Plus 样式 */

// 卡片
.el-card {
  background-color: var(--el-bg-color);
}

// 分页
.el-pagination {
  // 使用统一的响应式断点
  @media screen and (max-width: 767px) { // MD断点以下
    .el-pagination__total,
    .el-pagination__sizes,
    .el-pagination__jump,
    .btn-prev,
    .btn-next {
      display: none !important;
    }
  }

  // 在小屏幕上优化分页显示
  @media screen and (max-width: 575px) { // SM断点以下
    .el-pager {
      li {
        min-width: 28px;
        height: 28px;
        line-height: 28px;
        font-size: 12px;
      }
    }
  }
}
