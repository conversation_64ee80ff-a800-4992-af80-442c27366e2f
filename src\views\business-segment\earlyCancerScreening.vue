<!-- 
生命医疗 癌症早筛 页面 
-->
<template>
  <div class="business-details-main">
    <div class="sub-title">
      <div class="top-breadcrumb">
        <el-breadcrumb :separator-icon="ArrowRight">
          <el-breadcrumb-item :to="{ path: '/' }">{{ $t("menus.home") }}</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/business-segment' }">{{ $t("businessSegment.title") }}</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/business-segment/business-details' }">{{
            $t("businessSegment.scienceCenter") }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t("scienceCenter.biomedical") }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t("scienceCenter.earlyCancerScreening") }}</el-breadcrumb-item>

        </el-breadcrumb>
      </div>
    </div>

    <div class="content-main">
      <div class="content">
        <!-- 生命医疗 癌症早筛 -->
        <div class="img-container1">
          <el-image :src="EarlyCancerScreeninginfo.earlyCancerScreening_Img1" alt="癌症早筛"> </el-image>
          <div class="text-overlay">
            <div class="title">{{ EarlyCancerScreeninginfo[lang].sub_title1 }}</div>
            <div class="desc" v-html="EarlyCancerScreeninginfo[lang].sub_desc1"></div>
          </div>
        </div>
        <!-- 我们优势 -->
        <div class="advantages-title">
          {{ EarlyCancerScreeninginfo[lang].sub_title2 }}
        </div>

        <div class="advantages-content">
          <!-- 图文展示区 -->
          <div class="tabs-image-container">
            <div class="tabs-container">
              <el-tabs v-model="activeName" class="tabs-early-can" @tab-click="handleClick">
                <el-tab-pane :label="EarlyCancerScreeninginfo[lang].sub_title3" name="first">
                  <ul class="tab-list">
                    <li>{{ EarlyCancerScreeninginfo[lang].sub_desc3_l1 }}</li>
                    <li>{{ EarlyCancerScreeninginfo[lang].sub_desc3_l2 }}</li>
                    <li>{{ EarlyCancerScreeninginfo[lang].sub_desc3_l3 }}</li>
                  </ul>
                </el-tab-pane>
                <el-tab-pane :label="EarlyCancerScreeninginfo[lang].sub_title4" name="second">
                  <ul class="tab-list">
                    <li v-html="EarlyCancerScreeninginfo[lang].sub_desc4_l1"></li>
                    <li v-html="EarlyCancerScreeninginfo[lang].sub_desc4_l2"></li>
                    <li v-html="EarlyCancerScreeninginfo[lang].sub_desc4_l3"></li>
                  </ul>
                </el-tab-pane>
              </el-tabs>
            </div>

            <div class="image-container">
              <el-image
                v-if="activeName == 'first'"
                :src="EarlyCancerScreeninginfo.earlyCancerScreening_Img2"
                class="tab-image"
                fit="cover"
              />
              <el-image
                v-if="activeName == 'second'"
                :src="EarlyCancerScreeninginfo.earlyCancerScreening_Img3"
                class="tab-image"
                fit="cover"
              />
            </div>
          </div>

          <!-- 箭头按钮 -->
          <div class="navigation-buttons">
            <button class="nav-btn" @click="btnClick('l')">
              <el-icon><ArrowLeftBold /></el-icon>
            </button>
            <button class="nav-btn" @click="btnClick('r')">
              <el-icon><ArrowRightBold /></el-icon>
            </button>
          </div>
        </div>
        
      </div>
    </div>
  </div>
</template>

<script  lang="ts" setup>
import { nextTick, ref } from "vue"
import { ArrowLeftBold, ArrowRight, ArrowRightBold } from "@element-plus/icons-vue"
import { onBeforeMount, reactive } from "vue"

import earlyCancerScreening_info from "./earlyCancerScreening-info"


import { i18n } from "@/i18n"
import { watch } from "vue"

import { useRouter } from "vue-router"

const router = useRouter()
const EarlyCancerScreeninginfo = reactive(earlyCancerScreening_info)
const activeName = ref('first')

const btnClick = (item) => {
  if (activeName.value == 'first') {
    activeName.value = 'second'
  }
  else {
    activeName.value = 'first'
  }
  nextTick.apply();
}

const lang = ref(i18n.global.locale)

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  },
  () => activeName,
  
)

const imgClick = (path) =>{
  router.push({
    path: path,
    query: {
      // key: item.key
    }
  })
}
onBeforeMount(() => {
  console.log(earlyCancerScreening_info)
})
</script>

<style lang="scss" scoped>
 
.business-details-main {
  background: #f2f3f5;
  font-family: SourceHanSansCN, SourceHanSansCN;
  width: 100%;

  .sub-title {
    background: #f3f3f3;

    // 响应式高度
    height: 60px;

    @include respond-to(md) {
      height: 80px;
    }

    @include respond-to(lg) {
      height: 100px;
    }

    @include respond-to(fhd) {
      height: 120px;
    }

    .top-breadcrumb {
      @extend .responsive-container;
      color: rgba(0, 0, 0, 0.85);
      line-height: 1.4;

      // 响应式顶部间距
      padding-top: 1.5rem;

      @include respond-to(md) {
        padding-top: 2rem;
      }

      @include respond-to(lg) {
        padding-top: 3rem;
      }

      @include respond-to(fhd) {
        padding-top: 3.5rem;
      }

      :deep(.el-breadcrumb) {
        // 响应式字体大小
        font-size: 14px;

        @include respond-to(sm) {
          font-size: 16px;
        }

        @include respond-to(md) {
          font-size: 18px;
        }

        @include respond-to(fhd) {
          font-size: 20px;
        }

        @include respond-to(qhd) {
          font-size: 22px;
        }

        @include respond-to(uhd) {
          font-size: 26px;
        }

        &.el-breadcrumb__inner a:hover,
        .el-breadcrumb__inner.is-link:hover {
          color: inherit;
        }
      }
    }
  }

  .content-main {
    background: #f2f3f5;
  }

  .content {
    @include responsive-container;
    background-size: cover;

    // 响应式内边距
    padding: 2rem 0;

    @include respond-to(md) {
      padding: 3rem 0;
    }

    @include respond-to(lg) {
      padding: 4rem 0;
    }

    @include respond-to(fhd) {
      padding: 5rem 0;
    }

    @include respond-to(qhd) {
      padding: 6rem 0;
    }

    @include respond-to(uhd) {
      padding: 8rem 0;
    }

    // 优势标题样式
    .advantages-title {
      text-align: center;
      font-weight: 700;
      color: rgba(0, 0, 0, 0.85);

      // 响应式字体大小和间距
      font-size: 24px;
      line-height: 1.4;
      margin: 3rem 0;

      @include respond-to(sm) {
        font-size: 28px;
        margin: 4rem 0;
      }

      @include respond-to(md) {
        font-size: 32px;
        line-height: 1.5;
        margin: 5rem 0;
      }

      @include respond-to(lg) {
        font-size: 36px;
        margin: 6rem 0;
      }

      @include respond-to(fhd) {
        font-size: 40px;
        margin: 7rem 0;
      }

      @include respond-to(qhd) {
        font-size: 44px;
        margin: 8rem 0;
      }

      @include respond-to(uhd) {
        font-size: 52px;
        margin: 10rem 0;
      }
    }

    // 优势内容容器
    .advantages-content {
      display: flex;
      flex-direction: column;
      gap: 2rem;

      @include respond-to(lg) {
        gap: 3rem;
      }

      @include respond-to(fhd) {
        gap: 4rem;
      }
    }

    // 标签页和图片容器
    .tabs-image-container {
      display: flex;
      flex-direction: column;
      gap: 2rem;

      @include respond-to(lg) {
        flex-direction: row;
        align-items: flex-start;
        gap: 3rem;
      }

      @include respond-to(fhd) {
        gap: 4rem;
      }
    }

    // 标签页容器
    .tabs-container {
      flex: 1;

      @include respond-to(lg) {
        max-width: 50%;
      }

      .tab-list {
        list-style: none;
        padding: 0;
        margin: 1rem 0;

        li {
          padding: 0.75rem 0;
          color: rgba(0, 0, 0, 0.85);
          line-height: 1.6;
          border-bottom: 1px solid #e5e5e5;

          // 响应式字体大小
          font-size: 14px;

          @include respond-to(sm) {
            font-size: 15px;
            padding: 1rem 0;
          }

          @include respond-to(md) {
            font-size: 16px;
          }

          @include respond-to(lg) {
            font-size: 17px;
          }

          @include respond-to(fhd) {
            font-size: 18px;
            padding: 1.25rem 0;
          }

          @include respond-to(qhd) {
            font-size: 20px;
          }

          @include respond-to(uhd) {
            font-size: 24px;
            padding: 1.5rem 0;
          }

          &:last-child {
            border-bottom: none;
          }
        }
      }
    }

    // 图片容器
    .image-container {
      flex: 1;

      .tab-image {
        width: 100%;
        height: auto;
        border-radius: 12px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

        // 响应式最大高度
        max-height: 300px;
        object-fit: cover;

        @include respond-to(md) {
          max-height: 350px;
          border-radius: 16px;
        }

        @include respond-to(lg) {
          max-height: 400px;
        }

        @include respond-to(fhd) {
          max-height: 450px;
          border-radius: 20px;
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
        }

        @include respond-to(qhd) {
          max-height: 500px;
        }

        @include respond-to(uhd) {
          max-height: 600px;
          border-radius: 24px;
        }
      }
    }

    // 导航按钮
    .navigation-buttons {
      display: flex;
      justify-content: center;
      gap: 2rem;

      // 响应式间距
      margin: 2rem 0 4rem 0;

      @include respond-to(md) {
        margin: 3rem 0 6rem 0;
        gap: 3rem;
      }

      @include respond-to(lg) {
        margin: 4rem 0 8rem 0;
      }

      @include respond-to(fhd) {
        margin: 5rem 0 10rem 0;
        gap: 4rem;
      }

      @include respond-to(qhd) {
        margin: 6rem 0 12rem 0;
      }

      @include respond-to(uhd) {
        margin: 8rem 0 16rem 0;
        gap: 5rem;
      }

      .nav-btn {
        background: #1c2a77;
        color: white;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;

        // 响应式按钮大小
        width: 48px;
        height: 48px;
        font-size: 20px;

        @include respond-to(md) {
          width: 56px;
          height: 56px;
          font-size: 24px;
        }

        @include respond-to(lg) {
          width: 64px;
          height: 64px;
          font-size: 28px;
        }

        @include respond-to(fhd) {
          width: 72px;
          height: 72px;
          font-size: 32px;
        }

        @include respond-to(qhd) {
          width: 80px;
          height: 80px;
          font-size: 36px;
        }

        @include respond-to(uhd) {
          width: 96px;
          height: 96px;
          font-size: 44px;
        }

        &:hover {
          background: #409eff;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(28, 42, 119, 0.3);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }

    .info {
      .title {
        margin-left: 10%;
        width: 100%;
        font-weight: 600;
        font-size: 26px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 49px;
        letter-spacing: 2px;
      }

      .desc {
        margin: 10px 10%;
        font-size: 20px;
        color: #000000;
        line-height: 28px;
        letter-spacing: 2px;
      }
    }
    .img-container1 {
      position: relative;
      display: flex;
      /* 启用flex布局 */
      justify-content: center;
      /* 水平居中 */
      align-items: start;
      /* 垂直居中 */
      width: 100%;
      color: black;
    }
  
    .text-overlay {
      position: absolute;
      z-index: 1;
      /* 确保文字在图片上方 */
      color: black;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
      /* 文字阴影增强可读性 */
      margin: 10% 20%;
  
      .title {
        font-size: 48px;
      }
  
      .desc {
        margin-top: 28px;
        line-height: 32px;
        font-size: 24px;
        width: 100%;
        letter-spacing: 4px;
      }
    }
    .img-view {
      margin: 10px 0;
      width: 100%;
      height: auto;

      .img {
        display: block;
        width: 100%;
        height: auto;
      }
    }
    .sub-tag {
      margin: 10px 0;
      display: flex;
      width: 100%;
      flex-direction: column;
    }
    .sub-tag1 {
      margin-top: 18px;
      color: blue;
      font-size: 24px;
      font-weight: 600;
    }
    .sub-tag2 {
      font-size: 18px;
      font-weight: 600;
      margin-top: 20px;
    }
    .imgpos {
      vertical-align: middle;
    }
  }
 
  
  .tabs-early-can {
    :deep(.el-tabs__item.is-active, .el-tabs__item:hover) {
      color: black;
    }
    :deep(.el-tabs__item) {
      font-size: 36px;
      line-height: 54px;
      margin-bottom: 20px;
    }
    li {
      margin-top: 3rem;
    }
  }
   
    
  
  
}


    // 图片容器样式
    .img-container1 {
      position: relative;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;

      @include respond-to(md) {
        border-radius: 16px;
        margin-bottom: 3rem;
      }

      @include respond-to(fhd) {
        border-radius: 20px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
        margin-bottom: 4rem;
      }

      .text-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
        color: white;
        padding: 2rem;

        @include respond-to(md) {
          padding: 3rem;
        }

        @include respond-to(fhd) {
          padding: 4rem;
        }

        .title {
          font-weight: 700;
          margin-bottom: 1rem;

          // 响应式字体大小
          font-size: 20px;

          @include respond-to(sm) {
            font-size: 24px;
          }

          @include respond-to(md) {
            font-size: 28px;
            margin-bottom: 1.5rem;
          }

          @include respond-to(lg) {
            font-size: 32px;
          }

          @include respond-to(fhd) {
            font-size: 36px;
            margin-bottom: 2rem;
          }

          @include respond-to(qhd) {
            font-size: 40px;
          }

          @include respond-to(uhd) {
            font-size: 48px;
          }
        }

        .desc {
          line-height: 1.6;

          // 响应式字体大小
          font-size: 14px;

          @include respond-to(sm) {
            font-size: 15px;
          }

          @include respond-to(md) {
            font-size: 16px;
          }

          @include respond-to(lg) {
            font-size: 17px;
          }

          @include respond-to(fhd) {
            font-size: 18px;
          }

          @include respond-to(qhd) {
            font-size: 20px;
          }

          @include respond-to(uhd) {
            font-size: 24px;
          }
        }
      }
    }

    // 标签页样式优化
    :deep(.tabs-early-can) {
      .el-tabs__header {
        margin-bottom: 1.5rem;

        @include respond-to(md) {
          margin-bottom: 2rem;
        }

        @include respond-to(fhd) {
          margin-bottom: 2.5rem;
        }
      }

      .el-tabs__nav-wrap {
        &::after {
          background-color: #e4e7ed;
        }
      }

      .el-tabs__item {
        font-weight: 600;
        color: rgba(0, 0, 0, 0.65);

        // 响应式字体大小
        font-size: 14px;
        padding: 0 1rem;

        @include respond-to(sm) {
          font-size: 15px;
          padding: 0 1.25rem;
        }

        @include respond-to(md) {
          font-size: 16px;
          padding: 0 1.5rem;
        }

        @include respond-to(lg) {
          font-size: 17px;
        }

        @include respond-to(fhd) {
          font-size: 18px;
          padding: 0 2rem;
        }

        @include respond-to(qhd) {
          font-size: 20px;
        }

        @include respond-to(uhd) {
          font-size: 24px;
          padding: 0 2.5rem;
        }

        &.is-active {
          color: #1c2a77;
          font-weight: 700;
        }

        &:hover {
          color: #409eff;
        }
      }

      .el-tabs__active-bar {
        background-color: #1c2a77;
      }
    }
  }
</style>
