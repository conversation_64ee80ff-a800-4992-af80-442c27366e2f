<template>
  <div class="navigation-bar" :class="{ navigationBarMobile: isMobile }">
    <!-- 小屏菜单展开按钮 -->
    <Hamburger
      v-if="!isTop || isMobile"
      :is-active="appStore.sidebar.opened"
      class="hamburger"
      @toggle-click="toggleSidebar"
    />

    <Logo v-if="isMobile" :isMobile="isMobile" />

    <Breadcrumb v-if="!isTop || isMobile" class="breadcrumb" />
    <Sidebar v-if="isTop && !isMobile" class="sidebar" />
    <div class="right-menu">
      <SearchMenu v-if="showSearchMenu" class="right-menu-item" />
      <ScreenFull v-if="showScreenFull" class="right-menu-item" />
      <ThemeSwitch v-if="showThemeSwitch" class="right-menu-item" />
      <Notify v-if="showNotify" class="right-menu-item" />
    </div>

    <SelectLang v-if="!isMobile"></SelectLang>
  </div>
</template>

<script setup>
import { useRouter } from "vue-router"
import { storeToRefs } from "pinia"
import { useAppStore } from "@/store/modules/app"
import { useSettingsStore } from "@/store/modules/settings"
import { useUserStore } from "@/store/modules/user"
import Hamburger from "../Hamburger/index.vue"
import Breadcrumb from "../Breadcrumb/index.vue"
import Sidebar from "../Sidebar/index.vue"
import Notify from "@/components/Notify/index.vue"
import ThemeSwitch from "@/components/ThemeSwitch/index.vue"
import ScreenFull from "@/components/ScreenFull/index.vue"
import SearchMenu from "@/components/SearchMenu/index.vue"
import { useDevice } from "@/hooks/useDevice"
import { useLayoutMode } from "@/hooks/useLayoutMode"

import Logo from "../Logo/index.vue"

import SelectLang from "../select-lang/index.vue"

import { ref } from "vue"
const { isMobile } = useDevice()
const { isTop } = useLayoutMode()
const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()
const settingsStore = useSettingsStore()
const { showNotify, showThemeSwitch, showScreenFull, showSearchMenu } = storeToRefs(settingsStore)

const toggleSidebar = () => {
  appStore.toggleSidebar(false)
}
</script>

<style lang="scss" scoped>
.navigation-bar {
  height: var(--v3-navigationbar-height);
  overflow: hidden;
  color: var(--v3-navigationbar-text-color);
  display: flex;
  justify-content: flex-start;
  align-items: center;

  // 响应式顶部内边距
  padding-top: 1.5rem;

  @include respond-to(sm) {
    padding-top: 2rem;
  }

  @include respond-to(md) {
    padding-top: 2.5rem;
  }

  @include respond-to(lg) {
    padding-top: 3rem;
  }

  @include respond-to(xl) {
    padding-top: 3.5rem;
  }

  @include respond-to(fhd) {
    padding-top: 4rem;
  }

  @include respond-to(qhd) {
    padding-top: 4.5rem;
  }

  @include respond-to(uhd) {
    padding-top: 5rem;
  }

  .lang-box {
    display: flex;
    align-items: center;
    font-size: 12px;
    padding: 0 15px;

    p {
      cursor: pointer;
      color: #666666;

      &:hover {
        font-weight: bold;
      }
    }
  }

  .hamburger {
    display: flex;
    align-items: center;
    height: 60%;
    cursor: pointer;
    position: absolute;
    right: 5%;

    // 在移动端优化汉堡菜单的显示
    @media screen and (max-width: 991px) {
      right: 3%;
      z-index: 1001;
    }

    @media screen and (max-width: 575px) {
      right: 1rem;
    }
  }

  .breadcrumb {
    flex: 1;

    // 使用统一的响应式断点 - SM断点以下隐藏
    @media screen and (max-width: 575px) {
      display: none;
    }
  }

  .sidebar {
    flex: 1;
    // 设置 min-width 是为了让 Sidebar 里的 el-menu 宽度自适应
    min-width: 0px;

    :deep(.el-menu) {
      background-color: transparent;
    }

    :deep(.el-sub-menu) {
      &.is-active {
        .el-sub-menu__title {
          color: var(--el-color-primary) !important;
        }
      }
    }
  }

  .right-menu {
    height: 100%;
    display: flex;
    align-items: center;
    margin-right: 10px;

    .right-menu-item {
      padding: 0 10px;
      cursor: pointer;

      // 在小屏幕上减少间距
      @media screen and (max-width: 767px) {
        padding: 0 6px;
      }

      @media screen and (max-width: 575px) {
        padding: 0 4px;
      }

      .right-menu-avatar {
        display: flex;
        align-items: center;

        .el-avatar {
          margin-right: 10px;

          @media screen and (max-width: 575px) {
            margin-right: 6px;
            width: 28px;
            height: 28px;
          }
        }

        span {
          font-size: 16px;

          @media screen and (max-width: 575px) {
            font-size: 14px;
          }
        }
      }
    }
  }
}

.navigationBarMobile {
  padding: 0 15px;

  // 移动端导航栏优化
  @media screen and (max-width: 575px) {
    padding: 0 10px;
  }

  .hamburger {
    position: fixed;
    top: 20px;
    right: 15px;
    z-index: 1002;
  }

  .right-menu {
    margin-right: 50px; // 为汉堡菜单留出空间

    @media screen and (max-width: 575px) {
      margin-right: 45px;
    }
  }
}
</style>
