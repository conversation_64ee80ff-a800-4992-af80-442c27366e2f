// 业务页面通用响应式样式
// 用于 earlyCancerScreening.vue, geneTherapy.vue 等相似结构的页面

.business-details-main {
  background: #f2f3f5;
  font-family: SourceHanSansCN, SourceHanSansCN;
  width: 100%;
  
  .sub-title {
    background: #f3f3f3;
    
    // 响应式高度
    height: 60px;
    
    @include respond-to(md) {
      height: 80px;
    }
    
    @include respond-to(lg) {
      height: 100px;
    }
    
    @include respond-to(fhd) {
      height: 120px;
    }

    .top-breadcrumb {
      @include responsive-container;
      color: rgba(0, 0, 0, 0.85);
      line-height: 1.4;
      
      // 响应式顶部间距
      padding-top: 1.5rem;
      
      @include respond-to(md) {
        padding-top: 2rem;
      }
      
      @include respond-to(lg) {
        padding-top: 3rem;
      }
      
      @include respond-to(fhd) {
        padding-top: 3.5rem;
      }

      :deep(.el-breadcrumb) {
        // 响应式字体大小
        font-size: 14px;
        
        @include respond-to(sm) {
          font-size: 16px;
        }
        
        @include respond-to(md) {
          font-size: 18px;
        }
        
        @include respond-to(fhd) {
          font-size: 20px;
        }
        
        @include respond-to(qhd) {
          font-size: 22px;
        }
        
        @include respond-to(uhd) {
          font-size: 26px;
        }

        &.el-breadcrumb__inner a:hover,
        .el-breadcrumb__inner.is-link:hover {
          color: inherit;
        }
      }
    }
  }

  .content-main {
    background: #f2f3f5;
  }

  .content {
    @include responsive-container;
    background-size: cover;
    
    // 响应式内边距
    padding: 2rem 0;
    
    @include respond-to(md) {
      padding: 3rem 0;
    }
    
    @include respond-to(lg) {
      padding: 4rem 0;
    }
    
    @include respond-to(fhd) {
      padding: 5rem 0;
    }
    
    @include respond-to(qhd) {
      padding: 6rem 0;
    }
    
    @include respond-to(uhd) {
      padding: 8rem 0;
    }

    // 优势标题样式
    .advantages-title {
      text-align: center;
      font-weight: 700;
      color: rgba(0, 0, 0, 0.85);
      
      // 响应式字体大小和间距
      font-size: 24px;
      line-height: 1.4;
      margin: 3rem 0;
      
      @include respond-to(sm) {
        font-size: 28px;
        margin: 4rem 0;
      }
      
      @include respond-to(md) {
        font-size: 32px;
        line-height: 1.5;
        margin: 5rem 0;
      }
      
      @include respond-to(lg) {
        font-size: 36px;
        margin: 6rem 0;
      }
      
      @include respond-to(fhd) {
        font-size: 40px;
        margin: 7rem 0;
      }
      
      @include respond-to(qhd) {
        font-size: 44px;
        margin: 8rem 0;
      }
      
      @include respond-to(uhd) {
        font-size: 52px;
        margin: 10rem 0;
      }
    }
    
    // 优势内容容器
    .advantages-content {
      display: flex;
      flex-direction: column;
      gap: 2rem;
      
      @include respond-to(lg) {
        gap: 3rem;
      }
      
      @include respond-to(fhd) {
        gap: 4rem;
      }
    }
    
    // 标签页和图片容器
    .tabs-image-container {
      display: flex;
      flex-direction: column;
      gap: 2rem;
      
      @include respond-to(lg) {
        flex-direction: row;
        align-items: flex-start;
        gap: 3rem;
      }
      
      @include respond-to(fhd) {
        gap: 4rem;
      }
    }
    
    // 标签页容器
    .tabs-container {
      flex: 1;
      
      @include respond-to(lg) {
        max-width: 50%;
      }
      
      .tab-list {
        list-style: none;
        padding: 0;
        margin: 1rem 0;
        
        li {
          padding: 0.75rem 0;
          color: rgba(0, 0, 0, 0.85);
          line-height: 1.6;
          border-bottom: 1px solid #e5e5e5;
          
          // 响应式字体大小
          font-size: 14px;
          
          @include respond-to(sm) {
            font-size: 15px;
            padding: 1rem 0;
          }
          
          @include respond-to(md) {
            font-size: 16px;
          }
          
          @include respond-to(lg) {
            font-size: 17px;
          }
          
          @include respond-to(fhd) {
            font-size: 18px;
            padding: 1.25rem 0;
          }
          
          @include respond-to(qhd) {
            font-size: 20px;
          }
          
          @include respond-to(uhd) {
            font-size: 24px;
            padding: 1.5rem 0;
          }
          
          &:last-child {
            border-bottom: none;
          }
        }
      }
    }
    
    // 图片容器
    .image-container {
      flex: 1;
      
      .tab-image {
        width: 100%;
        height: auto;
        border-radius: 12px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        
        // 响应式最大高度
        max-height: 300px;
        object-fit: cover;
        
        @include respond-to(md) {
          max-height: 350px;
          border-radius: 16px;
        }
        
        @include respond-to(lg) {
          max-height: 400px;
        }
        
        @include respond-to(fhd) {
          max-height: 450px;
          border-radius: 20px;
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
        }
        
        @include respond-to(qhd) {
          max-height: 500px;
        }
        
        @include respond-to(uhd) {
          max-height: 600px;
          border-radius: 24px;
        }
      }
    }
    
    // 导航按钮
    .navigation-buttons {
      display: flex;
      justify-content: center;
      gap: 2rem;
      
      // 响应式间距
      margin: 2rem 0 4rem 0;
      
      @include respond-to(md) {
        margin: 3rem 0 6rem 0;
        gap: 3rem;
      }
      
      @include respond-to(lg) {
        margin: 4rem 0 8rem 0;
      }
      
      @include respond-to(fhd) {
        margin: 5rem 0 10rem 0;
        gap: 4rem;
      }
      
      @include respond-to(qhd) {
        margin: 6rem 0 12rem 0;
      }
      
      @include respond-to(uhd) {
        margin: 8rem 0 16rem 0;
        gap: 5rem;
      }
      
      .nav-btn {
        background: #1c2a77;
        color: white;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        
        // 响应式按钮大小
        width: 48px;
        height: 48px;
        font-size: 20px;
        
        @include respond-to(md) {
          width: 56px;
          height: 56px;
          font-size: 24px;
        }
        
        @include respond-to(lg) {
          width: 64px;
          height: 64px;
          font-size: 28px;
        }
        
        @include respond-to(fhd) {
          width: 72px;
          height: 72px;
          font-size: 32px;
        }
        
        @include respond-to(qhd) {
          width: 80px;
          height: 80px;
          font-size: 36px;
        }
        
        @include respond-to(uhd) {
          width: 96px;
          height: 96px;
          font-size: 44px;
        }
        
        &:hover {
          background: #409eff;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(28, 42, 119, 0.3);
        }
        
        &:active {
          transform: translateY(0);
        }
      }
    }

    // 图片容器样式
    .img-container1 {
      position: relative;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
      
      @include respond-to(md) {
        border-radius: 16px;
        margin-bottom: 3rem;
      }
      
      @include respond-to(fhd) {
        border-radius: 20px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
        margin-bottom: 4rem;
      }
      
      .text-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
        color: white;
        padding: 2rem;
        
        @include respond-to(md) {
          padding: 3rem;
        }
        
        @include respond-to(fhd) {
          padding: 4rem;
        }
        
        .title {
          font-weight: 700;
          margin-bottom: 1rem;
          
          // 响应式字体大小
          font-size: 20px;
          
          @include respond-to(sm) {
            font-size: 24px;
          }
          
          @include respond-to(md) {
            font-size: 28px;
            margin-bottom: 1.5rem;
          }
          
          @include respond-to(lg) {
            font-size: 32px;
          }
          
          @include respond-to(fhd) {
            font-size: 36px;
            margin-bottom: 2rem;
          }
          
          @include respond-to(qhd) {
            font-size: 40px;
          }
          
          @include respond-to(uhd) {
            font-size: 48px;
          }
        }
        
        .desc {
          line-height: 1.6;
          
          // 响应式字体大小
          font-size: 14px;
          
          @include respond-to(sm) {
            font-size: 15px;
          }
          
          @include respond-to(md) {
            font-size: 16px;
          }
          
          @include respond-to(lg) {
            font-size: 17px;
          }
          
          @include respond-to(fhd) {
            font-size: 18px;
          }
          
          @include respond-to(qhd) {
            font-size: 20px;
          }
          
          @include respond-to(uhd) {
            font-size: 24px;
          }
        }
      }
    }
  }
}
