<template>
  <div class="energy-valley-details">
    <div class="sub-title">
      <div class="top-breadcrumb">
        <el-breadcrumb :separator-icon="ArrowRight">
          <el-breadcrumb-item :to="{ path: '/' }">{{ $t("menus.home") }}</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/business-segment' }">{{
            $t("public.businessIntroduction")
          }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ businessSegmentInfo[lang].title }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>
    <div class="content">
      <div class="img-container1">
        <div class="text-overlay">
          <div class="title">{{ businessSegmentInfo[lang].title }}</div>
          <div class="desc" v-html="businessSegmentInfo[lang].homeDesc"></div>
        </div>
        <el-image :src="businessSegmentInfo.img" fit="cover" lazy />
      </div>

    </div>

    <div class="eight-themes">
      <div class="content">
        <div class="title">{{ $t("businessSegment.themesTitle") }}</div>
        <div class="themes-list">
          <div class="item">
            <el-image class="themesImg" :src="wellness" fit="cover" />
            <div class="desc">{{ $t("businessSegment.wellness") }}</div>
          </div>

          <div class="item">
            <el-image class="themesImg" :src="hospital" fit="cover" />
            <div class="desc">{{ $t("businessSegment.hospital") }}</div>
          </div>

          <div class="item">
            <el-image class="themesImg" :src="equestrianism" fit="cover" />
            <div class="desc">{{ $t("businessSegment.equestrianism") }}</div>
          </div>

          <div class="item">
            <el-image class="themesImg" :src="sailboat" fit="cover" />
            <div class="desc">{{ $t("businessSegment.sailboat") }}</div>
          </div>

          <div class="item">
            <el-image class="themesImg" :src="thangKa" fit="cover" />
            <div class="desc">{{ $t("businessSegment.thangka") }}</div>
          </div>

          <div class="item">
            <el-image class="themesImg" :src="exhibition" fit="cover" />
            <div class="desc">{{ $t("businessSegment.exhibition") }}</div>
          </div>

          <div class="item">
            <el-image class="themesImg" :src="institution" fit="cover" />
            <div class="desc">{{ $t("businessSegment.institution") }}</div>
          </div>

          <div class="item">
            <el-image class="themesImg" :src="foundation" fit="cover" />
            <div class="desc">{{ $t("businessSegment.foundation") }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="qiaoyao-energy-valley">
      <div class="content">
        <div class="main-box">
          <div class="left">
            <el-image class="themesImg" :src="qiaoYaoLeft" fit="cover" />
          </div>

          <div class="right-info">
            <div class="title">{{ $t("businessSegment.qiaoyaoTitle") }}</div>
            <div class="text" v-html="$t('businessSegment.qiaoyaoValleyText')"></div>

            <div class="img-list">
              <div class="item">
                <el-image class="themesImg" :src="rightFirst" fit="cover" />
                <div class="desc">{{ $t("businessSegment.rightFirst") }}</div>
              </div>

              <div class="item">
                <el-image class="themesImg" :src="rightSecond" fit="cover" />
                <div class="desc">{{ $t("businessSegment.rightSecond") }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="energy-valley-foundation">
      <div class="content">
        <div class="main-box">
          <div class="top">
            <div class="left-info">
              <div class="title">{{ $t("businessSegment.energyTitle") }}</div>
              <div class="line"></div>
              <div class="text" v-html="$t('businessSegment.energyText')"></div>
            </div>
            <div class="right">
              <el-image class="themesImg" :src="energyRight" fit="cover" />
            </div>
          </div>

          <div class="bottom">
            <div class="info-list">
              <div class="item">
                <div class="title">{{ $t("businessSegment.membershipService") }}</div>
                <div class="desc" v-html="$t('businessSegment.member_docs')"></div>
              </div>

              <div class="item">
                <div class="title">{{ $t("businessSegment.resourcePlatform") }}</div>
                <div class="desc" v-html="$t('businessSegment.resource_docs')"></div>
              </div>

              <div class="item">
                <div class="title">{{ $t("businessSegment.investPromoter") }}</div>
                <div class="desc" v-html="$t('businessSegment.invest_docs')"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, onBeforeMount } from "vue"
import { ArrowRight } from "@element-plus/icons-vue"
import wellness from "@/assets/business-segment/wellness.jpg"
import hospital from "@/assets/business-segment/hospital.jpg"
import equestrianism from "@/assets/business-segment/equestrianism.jpg"
import sailboat from "@/assets/business-segment/sailboat.jpg"
import thangKa from "@/assets/business-segment/thangka.jpg"
import exhibition from "@/assets/business-segment/exhibition.jpg"
import institution from "@/assets/business-segment/institution.jpg"
import foundation from "@/assets/business-segment/foundation.jpg"
import qiaoYaoLeft from "@/assets/business-segment/qiaoyao-left.png"
import rightFirst from "@/assets/business-segment/right-first.jpg"
import rightSecond from "@/assets/business-segment/right-scond.jpg"
import energyRight from "@/assets/business-segment/energy-right.jpg"
import BusinessSegmentList from "./business-segment-list"

import { i18n } from "@/i18n"
import { watch } from "vue"

import { useRouter } from "vue-router"

const router = useRouter()

const lang = ref(i18n.global.locale)

let businessSegmentInfo = reactive({
  key: "",
  title: "",
  desc: "",
  img: ""
})

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  }
)

const updateBusinessSegmentInfo = (key) => {
  const foundSegment = BusinessSegmentList.find((item) => item.key === key)
  if (foundSegment) {
    Object.assign(businessSegmentInfo, foundSegment)
  }
}

onBeforeMount(() => {
  updateBusinessSegmentInfo("GlobalEnergyValley")
})
</script>

<style lang="scss" scoped>
.energy-valley-details {
  .sub-title {
    background: #f3f3f3;

    // 响应式高度
    height: 60px;

    @include respond-to(md) {
      height: 80px;
    }

    @include respond-to(lg) {
      height: 100px;
    }

    @include respond-to(fhd) {
      height: 120px;
    }

    .top-breadcrumb {
      @extend .responsive-container;

      // 响应式顶部间距
      padding-top: 1.5rem;

      @include respond-to(md) {
        padding-top: 2rem;
      }

      @include respond-to(lg) {
        padding-top: 3rem;
      }

      @include respond-to(fhd) {
        padding-top: 3.5rem;
      }
      color: rgba(0, 0, 0, 0.85);
      line-height: 25px;
      // letter-spacing: 1px;

      :deep(.el-breadcrumb) {
        font-size: 18px;

        &.el-breadcrumb__inner a:hover,
        .el-breadcrumb__inner.is-link:hover {
          color: inherit;
        }
      }
    }
  }

  .content {
    width: 1200px;
    max-width: 100%;
    margin: 0 auto;
    padding: 150px 0;

    .info {
      display: flex;
      align-items: flex-start;
      gap: 50px;

      .title {
        min-width: 400px;

        font-weight: 600;
        font-size: 35px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 49px;
        letter-spacing: 1px;
      }

      .desc {
        font-size: 18px;
        color: #000000;
        line-height: 28px;
      }
    }

  .img-container1 {
    position: relative;
    display: flex;
    /* 启用flex布局 */
    justify-content: center;
    /* 水平居中 */
    align-items: start;
    /* 垂直居中 */
    width: 100%;
    color: black;
  }

  .text-overlay {
    position: absolute;
    z-index: 1;
    /* 确保文字在图片上方 */
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    /* 文字阴影增强可读性 */
    margin: 5% 10%;
    background-color: rgba(232, 223, 223, 0.3);
    padding: 40px 40px;

    .title {
      font-size: 48px;
    }

    .desc {
      margin-top: 28px;
      line-height: 32px;
      font-size: 24px;
      width: 100%;
      letter-spacing: 4px;
    }
  }
  }

  .eight-themes {
    .content {
      max-width: 1200px;
      width: 100%;
      margin: 0 auto;
      padding-top: 50px;
      padding-bottom: 50px;

      .title {
        font-size: 36px;
        color: #000000;
        font-weight: 600;
        text-align: center;
      }
      .themes-list {
        padding-top: 40px;
        display: grid;
        gap: 30px;
        grid-template-columns: repeat(4, 1fr);
      }

      .desc {
        font-size: 24px;
        text-align: center;
        color: #000000;
        font-weight: 600;
        padding-top: 18px;
      }

      .themesImg {
        width: 100%;
      }
    }
  }

  .qiaoyao-energy-valley {
    .content {
      max-width: 1200px;
      width: 100%;
      margin: 0 auto;
    }

    .left {
      width: 615px;
    }
    .right-info {
      padding-top: 30px;

      flex: 1;
      .title {
        font-size: 36px;
        color: #000000;
        font-weight: 600;
      }
      .text {
        padding: 40px 0;
        font-size: 18px;
        color: #000000;
      }
      .desc {
        font-size: 14px;
        color: #000000;
        text-align: center;
        padding-top: 8px;
      }
    }

    .main-box {
      display: flex;
      gap: 100px;
      align-items: center;
    }

    .img-list {
      display: flex;
      gap: 20px;

      .item {
        .themesImg {
          width: 100%;
          height: 150px;
        }
      }
    }
  }

  :deep(.energy-valley-foundation .content) {
    padding-top: 0px;
  }

  .energy-valley-foundation {
    .content {
      max-width: 1200px;
      width: 100%;
      margin: 0 auto;

      .top {
        display: flex;
        justify-content: space-between;
        gap: 30px;
        .left-info {
          flex: 1;

          .title {
            font-size: 36px;
            color: #000000;
            font-weight: 600;
          }
          .line {
            width: 80px;
            height: 1px;
            background: #979797;
            margin-top: 75px;
            margin-bottom: 28px;
          }

          .text {
            font-size: 18px;
            color: #000000;
          }
        }

        .right {
          width: 615px;
        }
      }

      .bottom {
        padding-top: 116px;
        .info-list {
          display: flex;
          justify-content: space-between;
          gap: 20px;
        }

        .item {
          position: relative;
          padding-bottom: 60px;
          .title {
            font-size: 24px;
            color: #000000;
            font-weight: 600;
          }

          .desc {
            font-size: 18px;
            color: #000000;
            padding-top: 15px;
          }

          &:before {
            content: "";
            display: block;
            width: 80px;
            height: 1px;
            background: #979797;
            position: absolute;
            bottom: 0;
          }
        }
      }
    }
  }
}

@media (max-width: 1340px) {
  .app-scrollbar .app-container-grow {
    padding: 0 20px;
  }
  .energy-valley-details .content .img-view {
    width: 100%;
  }
  .energy-valley-details .qiaoyao-energy-valley .main-box {
    gap: 50px;
  }

  .energy-valley-details .energy-valley-foundation .content .top .right {
    width: 500px;
  }
  .energy-valley-details .energy-valley-foundation .content .top .left-info .line {
    margin-top: 50px;
    margin-bottom: 20px;
  }
}

@media (max-width: 1024px) {
  .energy-valley-details .energy-valley-foundation .content .top .right {
    width: 400px;
  }
  .energy-valley-details .content .info .title {
    min-width: 250px;
  }
  .energy-valley-details .energy-valley-foundation .content .top .left-info .title {
    font-size: 30px;
  }

  .energy-valley-details .content {
    padding-top: 80px;
    padding-bottom: 80px;
  }

  .energy-valley-details .qiaoyao-energy-valley .img-list {
    justify-content: center;
  }

  .energy-valley-details .qiaoyao-energy-valley .main-box {
    flex-direction: column;
    align-items: center;
  }

  .energy-valley-details .qiaoyao-energy-valley .main-box {
    gap: 30px;
    padding-bottom: 70px;
  }

  .energy-valley-details .qiaoyao-energy-valley .right-info {
    text-align: left;
  }
  .energy-valley-details .qiaoyao-energy-valley .img-list {
    justify-content: center;
  }

  .energy-valley-details .energy-valley-foundation .content .top .right {
    width: 450px;
  }

  .energy-valley-details .energy-valley-foundation .content .bottom .item .line {
    margin-top: 30px;
    margin-bottom: 30px;
  }
}

@media (max-width: 820px) {
  .energy-valley-details .content .info .title {
    min-width: 180px;
  }

  .energy-valley-details .eight-themes .content .desc {
    font-size: 20px;
  }

  .energy-valley-details .energy-valley-foundation .content .top .left-info .line {
    margin-top: 36px;
  }

  .energy-valley-details .energy-valley-foundation .content .bottom {
    padding-top: 60px;
  }

  .energy-valley-details .energy-valley-foundation .content .bottom .info-list {
    padding: 0;
    flex-direction: column;
    gap: 20px;
  }

  .energy-valley-details .energy-valley-foundation .content .top {
    flex-direction: column;
    align-items: center;
  }

  .energy-valley-details .energy-valley-foundation .content .top .left-info {
    text-align: left;
  }
}

@media (max-width: 768px) {
  .energy-valley-details .energy-valley-foundation .content .top {
    flex-direction: column;
  }
  .energy-valley-details .content .info .title {
    font-size: 30px;
  }

  .energy-valley-details .eight-themes .content .themes-list {
    grid-template-columns: repeat(2, 1fr);
  }

  .energy-valley-details .qiaoyao-energy-valley .left {
    width: 100%;
  }
  .energy-valley-details .qiaoyao-energy-valley .right-info .title {
    font-size: 30px;
  }

  .energy-valley-details .eight-themes .content .title {
    text-align: left;
  }
}

@media (max-width: 576px) {
  .energy-valley-details .content .info {
    flex-direction: column;
  }
  .energy-valley-details .qiaoyao-energy-valley .img-list .item .themesImg {
    height: 100%;
  }
  .energy-valley-details .energy-valley-foundation .content .bottom .info-list {
    flex-direction: column;
  }

  .energy-valley-details .qiaoyao-energy-valley .left {
    width: 450px;
  }

  .energy-valley-details .content {
    padding-top: 30px;
    padding-bottom: 30px;
  }
}

@media (max-width: 480px) {
  .energy-valley-details .energy-valley-foundation .content .top .right {
    width: 100%;
  }

  .energy-valley-details .content .info .desc {
    font-size: 16px;
  }
  .energy-valley-details .qiaoyao-energy-valley .img-list {
    flex-direction: column;
  }

  .energy-valley-details .eight-themes .content .desc {
    font-size: 16px;
  }

  .energy-valley-details .eight-themes .content .title {
    font-size: 24px;
  }

  .energy-valley-details .content .img-view {
    margin-top: 40px;
  }

  .energy-valley-details .eight-themes .content {
    padding-top: 30px;
  }

  .energy-valley-details .qiaoyao-energy-valley .left {
    width: 350px;
  }

  .energy-valley-details .qiaoyao-energy-valley .right-info .title {
    font-size: 24px;
  }

  .energy-valley-details .qiaoyao-energy-valley .right-info .text {
    font-size: 16px;
    padding: 20px 0;
  }

  .energy-valley-details .energy-valley-foundation .content .bottom .item .desc {
    font-size: 16px;
  }

  .energy-valley-details .energy-valley-foundation .content .top .left-info .title {
    font-size: 24px;
  }

  .energy-valley-details .content .info {
    gap: 20px;
  }
  .energy-valley-details .qiaoyao-energy-valley .main-box {
    padding-bottom: 30px;
    gap: 0;
  }

  .energy-valley-details .eight-themes .content {
    padding-bottom: 30px;
  }

  .energy-valley-details .energy-valley-foundation .content .top .left-info .text {
    font-size: 16px;
  }

  .energy-valley-details .energy-valley-foundation .content .bottom {
    padding-top: 20px;
  }

  .energy-valley-details .energy-valley-foundation .content .bottom .item {
    padding-bottom: 20px;
  }

  .energy-valley-details .energy-valley-foundation .content .bottom .info-list {
    gap: 30px;
  }

  .energy-valley-details .eight-themes .content .title,
  .energy-valley-details .content .info .title,
  .energy-valley-details .qiaoyao-energy-valley .right-info .title {
    font-size: 22px;
  }

  .energy-valley-details .energy-valley-foundation .content .bottom .item .title {
    font-size: 20px;
  }
}

@media (max-width: 390px) {
  .energy-valley-details .energy-valley-foundation .content .bottom .item .title {
    font-size: 20px;
  }
}
@media (max-width: 380px) {
  .energy-valley-details .content .info .title {
    font-size: 22px;
  }

  .energy-valley-details .eight-themes .content .title {
    font-size: 22px;
  }

  .energy-valley-details .qiaoyao-energy-valley .right-info .title {
    font-size: 22px;
  }
  .energy-valley-details .content .info {
    gap: 20px;
  }
  .energy-valley-details .eight-themes .content .desc {
    font-size: 16px;
  }

  .energy-valley-details .energy-valley-foundation .content .top .right {
    width: 350px;
  }

  .energy-valley-details .energy-valley-foundation .content .top .left-info .title {
    font-size: 22px;
  }

  .energy-valley-details .energy-valley-foundation .content .bottom .item .title {
    font-size: 22px;
  }
}
</style>
