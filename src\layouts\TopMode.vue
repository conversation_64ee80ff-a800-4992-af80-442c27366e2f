<!--
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-06-05 18:22:56
-->
<template>
  <div class="app-wrapper">
    <!-- 头部导航栏和标签栏 -->
    <div class="layout-header" :class="{ 'fixed-header': fixedHeader }">
      <div class="content">
        <Logo v-if="showLogo" :collapse="false" class="logo" />
        <NavigationBar class="navigation-bar" />
      </div>

      <TagsView v-show="showTagsView" />
    </div>
    <!-- 主容器 -->
    <div :class="{ hasTagsView: showTagsView }" class="main-container">
      <!-- 页面主体内容 -->
      <AppMain class="app-main" :class="{ 'app-main-scroll': !fixedHeader }" />
    </div>
  </div>
</template>

<script setup>
import { storeToRefs } from "pinia"
import { useSettingsStore } from "@/store/modules/settings"
import { App<PERSON><PERSON>, NavigationBar, TagsView, Logo } from "./components"

const settingsStore = useSettingsStore()
const { showTagsView, showLogo, fixedHeader } = storeToRefs(settingsStore)
</script>

<style lang="scss" scoped>
@import "@/styles/mixins.scss";
$transition-time: 0.35s;

.app-wrapper {
  @extend %clearfix;
  width: 100%;
}

.fixed-header {
  position: fixed;
  top: 0;
  z-index: 1002;
  width: 100%;
  .logo {
    // width: var(--v3-sidebar-width);
    // width: auto;
    // padding: 5px 15px;
  }
}

.layout-header {
  background-color: var(--v3-header-bg-color);
  box-shadow: var(--v3-header-box-shadow);
  border: none;

  .content {
    @include responsive-container;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;

    // 响应式内边距
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;

    @include respond-to(md) {
      padding-top: 0.75rem;
      padding-bottom: 0.75rem;
    }

    @include respond-to(lg) {
      padding-top: 1rem;
      padding-bottom: 1rem;
    }

    @include respond-to(fhd) {
      padding-top: 1.25rem;
      padding-bottom: 1.25rem;
    }

    @include respond-to(qhd) {
      padding-top: 1.5rem;
      padding-bottom: 1.5rem;
    }

    @include respond-to(uhd) {
      padding-top: 2rem;
      padding-bottom: 2rem;
    }

    .navigation-bar {
      flex: 1;
    }
  }
}

.main-container {
  min-height: 100%;
}

.app-main {
  transition: padding-left $transition-time;
  padding-top: var(--v3-navigationbar-height);
  height: 100vh;
  // overflow: auto;
}

.hasTagsView {
  .app-main {
    padding-top: var(--v3-header-height);
  }
}

.app-main-scroll {
  padding-top: 0px;
  height: auto;
}

@media (max-width: 1320px) {
  .fixed-header .content {
    padding: 0 20px;
  }
}
@media (max-width: 1024px) {
}
@media (max-width: 820px) {
}
@media (max-width: 768px) {
}
@media (max-width: 576px) {
}
@media (max-width: 480px) {
}
@media (max-width: 380px) {
}
</style>
