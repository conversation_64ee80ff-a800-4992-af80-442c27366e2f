<template>
  <div class="home-carousel-view">
    <el-carousel class="home-carousel" motion-blur :autoplay="false">
      <el-carousel-item v-for="(item, index) in carouselList" :key="index" @click="gotoGreatHealth(item)">
        <div class="home-greatHealth-view" v-if="item?.type == 'greatHealth'">
          <div class="txt-s">{{ item[lang].desc1 }}</div>
          <div class="txt-s">{{ item[lang].desc2 }}</div>
          <div class="txt-tip">{{ item[lang].desc3 }}</div>

          <div class="view-tips">
            <div class="tips-i">
              <span>{{ item[lang].tipTxt }}</span
              ><el-icon><Right /></el-icon>
            </div>
          </div>
        </div>
        <div v-else class="txt">{{ item[lang] }}</div>
        <el-image class="carousel-image" :src="item.img" fit="cover" />
      </el-carousel-item>
    </el-carousel>
  </div>
</template>
<script setup>
import { ref, watch } from "vue"
import homeBg from "@/assets/home/<USER>"
import homeBg2 from "@/assets/home/<USER>"
import homeImg1 from "@/assets/home/<USER>"
import goodLifeService from "@/assets/home/<USER>"

import { i18n } from "@/i18n"
import { useRouter } from "vue-router"

const router = useRouter()

const lang = ref(i18n.global.locale)

const carouselList = ref([
  {
    img: goodLifeService,
    zh: "美好生活服务商",
    en: "Good Life Service Provider",
    zh_hk: "美好生活服務商",
  },
    {
    img: homeImg1,
    zh: {
      desc1: "中兆国际集团",
      desc2: "大健康板块隆重上市",
      desc3: "各界大咖明星齐祝福、共庆辉煌时刻",
      tipTxt: "前往观看视频"
    },
    en: {
      desc1: "Zhongzhao International Group",
      desc2: "The Comprehensive Health Segment is Officially Launched",
      desc3: "Wishes from Celebrities to Mark this Moment",
      tipTxt: "Watch the video"
    },
    zh_hk: {
      desc1: "中兆國際集團",
      desc2: "大健康板塊隆重上市",
      desc3: "各界大咖明星齊祝福、共慶輝煌時刻",
      tipTxt: "前往觀看視頻"
    },
    type: "greatHealth"
  },
  {
    img: homeBg,
    zh: "安全 健康 可持续发展",
    en: "Safe healthy and sustainable development",
    zh_hk: "安全 健康 可持續發展"
  },
  {
    img: homeBg2,
    zh: "成为全人类安全 健康和环境的坚定支持者",
    en: "To be a strong supporter of safety, health and the environment for all",
    zh_hk: "成為全人類安全 健康和環境的堅定支持者"
  }
])

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  }
)

const gotoGreatHealth = (item) => {
  if (item?.type == "greatHealth") {
    router.push({ path: "/great-health" })
  }
}
</script>
<style lang="scss" scoped>
.home-carousel-view {
  .home-carousel {
    position: relative;
    background: #000000;
    width: 100%;

    // 响应式高度 - 移动端优先，单列流式布局友好
    height: 350px;

    @include respond-to(sm) {
      height: 400px;
    }

    @include respond-to(md) {
      height: 450px;
    }

    @include respond-to(lg) {
      height: 500px;
    }

    @include respond-to(xl) {
      height: 600px;
    }

    @include respond-to(xxl) {
      height: 650px;
    }

    // 全高清桌面 (1080p) - 主要基准
    @include respond-to(fhd) {
      height: 700px;
    }

    // 2K显示器优化
    @include respond-to(qhd) {
      height: 800px;
    }

    // 4K显示器优化
    @include respond-to(uhd) {
      height: 1000px;
    }
    :deep(.el-carousel__container) {
      height: 100%;
    }

    :deep(.el-carousel__arrow) {
      top: 50%;
      color: #d2d2d2;
      background: none;
      font-size: 28px;

      @include respond-to(sm) {
        font-size: 32px;
      }

      @include respond-to(md) {
        font-size: 36px;
      }

      @include respond-to(lg) {
        font-size: 40px;
      }

      @include respond-to(fhd) {
        font-size: 44px;
      }

      @include respond-to(qhd) {
        font-size: 48px;
      }

      @include respond-to(uhd) {
        font-size: 56px;
      }
    }

    :deep(.el-carousel__indicators) {
      .el-carousel__button {
        height: 6px;
        border-radius: 5px;

        @include respond-to(fhd) {
          height: 8px;
        }

        @include respond-to(qhd) {
          height: 10px;
        }

        @include respond-to(uhd) {
          height: 12px;
        }
      }
    }

    :deep(.el-carousel__arrow--right) {
      right: 2%;

      @include respond-to(sm) {
        right: 3%;
      }

      @include respond-to(md) {
        right: 4%;
      }

      @include respond-to(lg) {
        right: 5%;
      }

      @include respond-to(xl) {
        right: 8%;
      }

      @include respond-to(fhd) {
        right: 10%;
      }
    }

    :deep(.el-carousel__arrow--left) {
      left: 2%;

      @include respond-to(sm) {
        left: 3%;
      }

      @include respond-to(md) {
        left: 4%;
      }

      @include respond-to(lg) {
        left: 5%;
      }

      @include respond-to(xl) {
        left: 8%;
      }

      @include respond-to(fhd) {
        left: 10%;
      }
    }

    .home-greatHealth-view,
    .txt {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      color: #ffffff;
      z-index: 1;
      font-weight: 600;
      text-shadow: 1px 1px 10px rgba(#ffffff, 0.1);

      // 响应式字体大小 - 移动端优先
      font-size: 20px;
      letter-spacing: 1px;

      @include respond-to(sm) {
        font-size: 24px;
        letter-spacing: 1.5px;
      }

      @include respond-to(md) {
        font-size: 28px;
        letter-spacing: 2px;
      }

      @include respond-to(lg) {
        font-size: 32px;
        letter-spacing: 2.5px;
      }

      @include respond-to(xl) {
        font-size: 36px;
        letter-spacing: 3px;
      }

      @include respond-to(xxl) {
        font-size: 40px;
        letter-spacing: 3px;
      }

      // 全高清桌面 (1080p) - 主要基准
      @include respond-to(fhd) {
        font-size: 44px;
        letter-spacing: 3.5px;
      }

      // 2K显示器优化
      @include respond-to(qhd) {
        font-size: 48px;
        letter-spacing: 4px;
      }

      // 4K显示器优化
      @include respond-to(uhd) {
        font-size: 56px;
        letter-spacing: 5px;
      }
    }

    .home-greatHealth-view {
      cursor: pointer;
      max-width: 95%;
      padding: 0 1rem;

      @include respond-to(md) {
        max-width: 90%;
        padding: 0 1.5rem;
      }

      @include respond-to(lg) {
        max-width: 1000px;
        padding: 0 2rem;
      }

      @include respond-to(xl) {
        max-width: 1200px;
      }

      @include respond-to(fhd) {
        max-width: 1400px;
      }

      @include respond-to(qhd) {
        max-width: 1600px;
      }

      @include respond-to(uhd) {
        max-width: 2000px;
      }

      .txt-tip {
        font-family: SourceHanSansCN, SourceHanSansCN;
        font-weight: 500;
        color: #90d6ff;
        letter-spacing: 1px;
        text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);

        // 响应式字体大小
        font-size: 16px;
        line-height: 28px;

        @include respond-to(sm) {
          font-size: 18px;
          line-height: 32px;
          letter-spacing: 1.5px;
        }

        @include respond-to(md) {
          font-size: 20px;
          line-height: 36px;
          letter-spacing: 2px;
        }

        @include respond-to(lg) {
          font-size: 24px;
          line-height: 42px;
        }

        @include respond-to(xl) {
          font-size: 26px;
          line-height: 46px;
        }

        @include respond-to(xxl) {
          font-size: 28px;
          line-height: 50px;
        }

        @include respond-to(fhd) {
          font-size: 30px;
          line-height: 54px;
        }

        @include respond-to(qhd) {
          font-size: 34px;
          line-height: 60px;
        }

        @include respond-to(uhd) {
          font-size: 40px;
          line-height: 70px;
        }
      }

      .view-tips {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        .tips-i {
          display: flex;
          align-items: center;
          gap: 5px;
          margin-top: 50px;
          cursor: pointer;
        }

        span {
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: 500;
          font-size: 20px;
          color: #ffffff;
          letter-spacing: 1px;
          text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .el-icon {
          font-size: 30px;
        }
      }
    }

    .txt {
      width: 100%;
      text-align: center;
    }

    .carousel-image {
      display: block;
      width: 100%;
      height: 100%;
    }

    video {
      display: block;
      width: 100%;
      height: 100%;

      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;

      object-fit: unset;

      z-index: 50;
    }
  }
}
@import "./media.scss";
</style>
