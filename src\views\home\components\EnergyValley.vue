<template>
  <div class="energy-valley">
    <div class="energy-valley-main">
      <div class="info-view">
        <div class="left">
          <el-image class="img" :src="EnergyValley" fit="cover" lazy />
        </div>
        <div class="right" @click="gotoEnergyDetail()">
          <h3 class="science-title">{{ $t("home.energyValley") }}</h3>
          <p class="desc">{{ $t("home.energy_desc1") }}</p>
          <p class="desc">{{ $t("home.energy_desc2") }}</p>
          <p class="desc">{{ $t("home.energy_desc3") }}</p>
          <div class="more-btn">
            <div class="more-view">
              {{ $t("public.learnMore")
              }}<span class="icon"
                ><el-icon><Right /></el-icon
              ></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import EnergyValley from "@/assets/home/<USER>"
import { useRouter } from "vue-router"
const router = useRouter()
const gotoEnergyDetail = () => {
  router.push("/business-segment/energy-valley-details")
}
</script>

<style lang="scss" scoped>
.energy-valley {
  .energy-valley-main {
    @include responsive-container;

    // 响应式顶部间距
    padding-top: 3rem;

    @include respond-to(md) {
      padding-top: 4rem;
    }

    @include respond-to(lg) {
      padding-top: 5rem;
    }

    @include respond-to(xl) {
      padding-top: 6rem;
    }

    @include respond-to(fhd) {
      padding-top: 7rem;
    }

    @include respond-to(qhd) {
      padding-top: 8rem;
    }

    @include respond-to(uhd) {
      padding-top: 10rem;
    }

    .info-view {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2rem;

      @include respond-to(lg) {
        flex-direction: row;
        align-items: center;
        gap: 0;
      }

      &:hover {
        .right {
          background: #409eff;
          transform: translateY(-2px);

          .science-title,
          .desc,
          .more-btn {
            color: #ffffff;
          }
        }
      }

      .left {
        flex: 1;
        position: relative;
        z-index: 20;
        width: 100%;
        max-width: 600px;

        // 响应式高度
        height: 250px;

        @include respond-to(sm) {
          height: 300px;
        }

        @include respond-to(md) {
          height: 350px;
          max-width: 700px;
        }

        @include respond-to(lg) {
          height: 400px;
          max-width: 800px;
        }

        @include respond-to(xl) {
          height: 450px;
        }

        @include respond-to(fhd) {
          height: 500px;
        }

        @include respond-to(qhd) {
          height: 600px;
        }

        @include respond-to(uhd) {
          height: 800px;
        }

        box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15);
        border-radius: 8px;
        overflow: hidden;

        @include respond-to(lg) {
          border-radius: 12px;
          box-shadow: 0px 6px 24px rgba(0, 0, 0, 0.2);
        }

        @include respond-to(fhd) {
          border-radius: 16px;
          box-shadow: 0px 8px 32px rgba(0, 0, 0, 0.25);
        }

        .img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .right {
        flex: 1;
        background: #f3f3f3;
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 8px;

        // 移动端样式
        padding: 2rem;
        position: relative;
        z-index: 10;

        @include respond-to(md) {
          padding: 2.5rem;
          border-radius: 12px;
        }

        @include respond-to(lg) {
          padding: 3rem 3rem 3rem 5rem;
          margin-left: -3rem;
          border-radius: 0 12px 12px 0;
        }

        @include respond-to(xl) {
          padding: 4rem 4rem 4rem 6rem;
          margin-left: -4rem;
        }

        @include respond-to(fhd) {
          padding: 5rem 5rem 5rem 8rem;
          margin-left: -5rem;
          border-radius: 0 16px 16px 0;
        }

        @include respond-to(qhd) {
          padding: 6rem 6rem 6rem 10rem;
          margin-left: -6rem;
        }

        @include respond-to(uhd) {
          padding: 8rem 8rem 8rem 12rem;
          margin-left: -8rem;
        }

        .science-title {
          font-weight: 700;
          margin-bottom: 1rem;
          color: #1f2a72;

          // 响应式字体大小
          font-size: 20px;

          @include respond-to(sm) {
            font-size: 24px;
            margin-bottom: 1.25rem;
          }

          @include respond-to(md) {
            font-size: 28px;
            margin-bottom: 1.5rem;
          }

          @include respond-to(lg) {
            font-size: 30px;
            margin-bottom: 1.75rem;
          }

          @include respond-to(xl) {
            font-size: 32px;
            margin-bottom: 2rem;
          }

          @include respond-to(fhd) {
            font-size: 36px;
            margin-bottom: 2.25rem;
          }

          @include respond-to(qhd) {
            font-size: 40px;
            margin-bottom: 2.5rem;
          }

          @include respond-to(uhd) {
            font-size: 48px;
            margin-bottom: 3rem;
          }
        }

        .desc {
          color: rgba(0, 0, 0, 0.85);
          text-align: left;
          line-height: 1.6;
          margin-bottom: 0.75rem;

          // 响应式字体大小和字间距
          font-size: 14px;
          letter-spacing: 1px;

          @include respond-to(sm) {
            font-size: 15px;
            letter-spacing: 1.5px;
          }

          @include respond-to(md) {
            font-size: 16px;
            letter-spacing: 1.5px;
            margin-bottom: 1rem;
          }

          @include respond-to(lg) {
            font-size: 17px;
            letter-spacing: 2px;
          }

          @include respond-to(xl) {
            font-size: 18px;
            letter-spacing: 2px;
          }

          @include respond-to(fhd) {
            font-size: 20px;
            letter-spacing: 2.5px;
          }

          @include respond-to(qhd) {
            font-size: 22px;
            letter-spacing: 3px;
          }

          @include respond-to(uhd) {
            font-size: 26px;
            letter-spacing: 3.5px;
          }
        }

        .more-btn {
          display: flex;
          align-items: center;
          font-weight: 600;
          color: #1f2a72;

          // 响应式间距和字体
          margin-top: 1.5rem;
          font-size: 14px;

          @include respond-to(sm) {
            margin-top: 2rem;
            font-size: 15px;
          }

          @include respond-to(md) {
            margin-top: 2.5rem;
            font-size: 16px;
          }

          @include respond-to(lg) {
            margin-top: 3rem;
          }

          @include respond-to(fhd) {
            font-size: 18px;
            margin-top: 3.5rem;
          }

          @include respond-to(qhd) {
            font-size: 20px;
            margin-top: 4rem;
          }

          @include respond-to(uhd) {
            font-size: 24px;
            margin-top: 5rem;
          }

          .more-view {
            display: flex;
            align-items: center;
          }
        }

        .icon {
          padding-left: 0.5rem;
          display: flex;
          align-items: center;

          // 响应式图标大小
          font-size: 16px;

          @include respond-to(md) {
            font-size: 18px;
            padding-left: 0.75rem;
          }

          @include respond-to(lg) {
            font-size: 20px;
          }

          @include respond-to(fhd) {
            font-size: 22px;
          }

          @include respond-to(qhd) {
            font-size: 24px;
          }

          @include respond-to(uhd) {
            font-size: 28px;
          }
        }
      }
    }
  }
}

@import "./media.scss";
</style>
