<template>
  <div class="news-content">
    <h2 class="news-title">{{ $t("news.title") }}</h2>
    <div class="news-list">
      <div class="item" v-for="item in newsListData" :key="item.id" @click="goDetails(item.id)">
        <div class="item-img">
          <el-image class="img" :src="item.newBgImg" alt="" fit="cover" />
        </div>
        <div class="item-content">
          <div class="item-title" v-if="refreshNews">{{ getItemTitle(item) }}</div>
          <div class="item-time">
            <span>{{ getNewTime(item.newTime) }}</span>
            <span class="icon"
              ><el-icon><Right /></el-icon
            ></span>
          </div>
        </div>
      </div>
    </div>
    <div class="more-btn" v-if="hasNewsData">
      <div class="btn" @click="getMoreNews">{{ $t("public.loadingMore") }}</div>
    </div>
  </div>
</template>
<script setup>
import { nextTick, onMounted, reactive, ref, watch } from "vue"

import { useRouter } from "vue-router"
import { i18n } from "@/i18n"

import dayjs from "dayjs"
import { newsList } from "@/api/new/index.js"

const refreshNews = ref(true)

const router = useRouter()
const lang = ref(i18n.global.locale)

const page = ref(1)
const pageSize = ref(9)
const loading = ref(false)
const hasNewsData = ref(true)
const newsListData = ref([])

const getNewsList = () => {
  if (loading.value) return

  loading.value = true

  const params = {
    page: page.value,
    pageSize: pageSize.value
  }
  newsList(params)
    .then((res) => {
      if (res.code == 200) {
        //根据 sort 排序
        res.data.sort((a, b) => a.sort - b.sort)
        newsListData.value = [...newsListData.value, ...res.data]

        if (res.data.length < pageSize.value) {
          hasNewsData.value = false
        }

        nextTick(() => {
          refreshNewsList()
        })
      }
    })
    .finally(() => {
      loading.value = false
    })
}
getNewsList()

const getMoreNews = () => {
  page.value++
  getNewsList()
}

const getItemTitle = (item) => {
  const selectMap = {
    zh: item.titleZh,
    zh_hk: item.titleHk,
    en: item.titleEn
  }
  return selectMap[lang.value]
}

const getNewTime = (time) => {
  return dayjs(time).format("YYYY-MM-DD")
}

const goDetails = (id) => {
  router.push({ name: "NewsDetails", query: { id } })
}

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"

    refreshNews.value = false
    nextTick(() => {
      refreshNews.value = true

      nextTick(() => {
        refreshNewsList()
      })
    })
  }
)

const refreshNewsList = () => {
  const itemTitle = document.querySelectorAll(".item-title")
  let maxHeight = 0
  itemTitle.forEach((item) => {
    maxHeight = Math.max(maxHeight, item.clientHeight)
  })
  itemTitle.forEach((item) => {
    item.style.height = `${maxHeight}px`
  })
}
</script>

<style lang="scss" scoped>
.news-content {
  // 响应式顶部和底部间距
  padding-top: 3rem;
  padding-bottom: 4rem;

  @include respond-to(md) {
    padding-top: 4rem;
    padding-bottom: 5rem;
  }

  @include respond-to(lg) {
    padding-top: 5rem;
    padding-bottom: 6rem;
  }

  @include respond-to(xl) {
    padding-top: 6rem;
    padding-bottom: 7rem;
  }

  @include respond-to(fhd) {
    padding-top: 8rem;
    padding-bottom: 9rem;
  }

  @include respond-to(qhd) {
    padding-top: 9rem;
    padding-bottom: 10rem;
  }

  @include respond-to(uhd) {
    padding-top: 12rem;
    padding-bottom: 14rem;
  }

  .news-title {
    color: rgba(0, 0, 0, 0.85);
    text-align: center;
    font-weight: 700;

    // 响应式字体大小和字间距
    font-size: 28px;
    letter-spacing: 2px;
    margin-bottom: 2rem;

    @include respond-to(sm) {
      font-size: 32px;
      letter-spacing: 3px;
      margin-bottom: 2.5rem;
    }

    @include respond-to(md) {
      font-size: 36px;
      letter-spacing: 3px;
      margin-bottom: 3rem;
    }

    @include respond-to(lg) {
      font-size: 42px;
      letter-spacing: 4px;
      margin-bottom: 3.5rem;
    }

    @include respond-to(xl) {
      font-size: 46px;
      letter-spacing: 4px;
      margin-bottom: 4rem;
    }

    @include respond-to(fhd) {
      font-size: 50px;
      letter-spacing: 5px;
      margin-bottom: 4.5rem;
    }

    @include respond-to(qhd) {
      font-size: 56px;
      letter-spacing: 6px;
      margin-bottom: 5rem;
    }

    @include respond-to(uhd) {
      font-size: 64px;
      letter-spacing: 7px;
      margin-bottom: 6rem;
    }
  }

  .news-list {
    @extend .responsive-container;
    @extend .responsive-grid;

    // 响应式顶部间距
    padding-top: 2rem;

    @include respond-to(md) {
      padding-top: 2.5rem;
    }

    @include respond-to(lg) {
      padding-top: 3rem;
    }

    @include respond-to(fhd) {
      padding-top: 3.5rem;
    }

    @include respond-to(qhd) {
      padding-top: 4rem;
    }

    @include respond-to(uhd) {
      padding-top: 5rem;
    }

    // 移动端单列流式布局
    .item {
      @extend .responsive-grid-item;
      @extend .col-3; // 桌面端3列布局

      // 确保在超大屏幕上卡片不会过大
      @include respond-to(fhd) {
        max-width: 400px;
      }

      @include respond-to(qhd) {
        max-width: 450px;
      }

      @include respond-to(uhd) {
        max-width: 500px;
      }
    }
  }

  .more-btn {
    display: flex;
    justify-content: center;

    // 响应式间距
    margin-top: 2rem;

    @include respond-to(md) {
      margin-top: 2.5rem;
    }

    @include respond-to(lg) {
      margin-top: 3rem;
    }

    @include respond-to(fhd) {
      margin-top: 3.5rem;
    }

    @include respond-to(qhd) {
      margin-top: 4rem;
    }

    @include respond-to(uhd) {
      margin-top: 5rem;
    }

    .btn {
      color: #fff;
      background: #1c2a77;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      border: none;

      // 响应式字体和内边距
      font-size: 16px;
      padding: 0.75rem 1.5rem;

      @include respond-to(md) {
        font-size: 18px;
        padding: 1rem 2rem;
        border-radius: 10px;
      }

      @include respond-to(lg) {
        font-size: 20px;
        padding: 1.25rem 2.5rem;
      }

      @include respond-to(fhd) {
        font-size: 22px;
        padding: 1.5rem 3rem;
        border-radius: 12px;
      }

      @include respond-to(qhd) {
        font-size: 24px;
        padding: 1.75rem 3.5rem;
      }

      @include respond-to(uhd) {
        font-size: 28px;
        padding: 2rem 4rem;
        border-radius: 16px;
      }

      &:hover {
        background: #409eff;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(28, 42, 119, 0.3);
      }
    }
  }

  .item {
    background: #ffffff;
    border-radius: 12px;
    transition: all 0.3s ease;
    overflow: hidden;

    // 响应式阴影
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    @include respond-to(lg) {
      border-radius: 16px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    @include respond-to(fhd) {
      border-radius: 20px;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    }

    @include respond-to(qhd) {
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.14);
    }

    @include respond-to(uhd) {
      border-radius: 24px;
      box-shadow: 0 10px 32px rgba(0, 0, 0, 0.16);
    }

    &:hover {
      cursor: pointer;
      transform: translateY(-8px);

      // 响应式悬停阴影
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);

      @include respond-to(lg) {
        transform: translateY(-10px);
        box-shadow: 0 12px 32px rgba(0, 0, 0, 0.18);
      }

      @include respond-to(fhd) {
        transform: translateY(-12px);
        box-shadow: 0 16px 40px rgba(0, 0, 0, 0.2);
      }

      @include respond-to(qhd) {
        transform: translateY(-14px);
        box-shadow: 0 20px 48px rgba(0, 0, 0, 0.22);
      }

      @include respond-to(uhd) {
        transform: translateY(-16px);
        box-shadow: 0 24px 56px rgba(0, 0, 0, 0.25);
      }
    }

    .item-img {
      // 响应式图片高度
      height: 180px;

      @include respond-to(sm) {
        height: 200px;
      }

      @include respond-to(md) {
        height: 220px;
      }

      @include respond-to(lg) {
        height: 240px;
      }

      @include respond-to(xl) {
        height: 250px;
      }

      @include respond-to(fhd) {
        height: 280px;
      }

      @include respond-to(qhd) {
        height: 320px;
      }

      @include respond-to(uhd) {
        height: 400px;
      }

      .img {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .item-content {
      // 响应式内边距
      padding: 1rem 1.25rem 1rem 1.25rem;

      @include respond-to(md) {
        padding: 1.25rem 1.5rem 1rem 1.5rem;
      }

      @include respond-to(lg) {
        padding: 1.5rem 1.75rem 1.25rem 1.75rem;
      }

      @include respond-to(fhd) {
        padding: 1.75rem 2rem 1.5rem 2rem;
      }

      @include respond-to(qhd) {
        padding: 2rem 2.25rem 1.75rem 2.25rem;
      }

      @include respond-to(uhd) {
        padding: 2.5rem 2.75rem 2rem 2.75rem;
      }

      .item-title {
        color: rgba(0, 0, 0, 0.85);
        font-weight: 700;
        width: 100%;
        line-height: 1.4;

        // 响应式字体大小
        font-size: 14px;

        @include respond-to(sm) {
          font-size: 15px;
        }

        @include respond-to(md) {
          font-size: 16px;
        }

        @include respond-to(lg) {
          font-size: 17px;
        }

        @include respond-to(xl) {
          font-size: 18px;
        }

        @include respond-to(fhd) {
          font-size: 20px;
        }

        @include respond-to(qhd) {
          font-size: 22px;
        }

        @include respond-to(uhd) {
          font-size: 26px;
        }
      }
    }

    .item-text {
      color: #6d7278;
      text-align: left;
      display: -webkit-box;
      overflow: hidden;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      box-sizing: border-box;
      line-height: 1.5;

      // 响应式字体和间距
      font-size: 12px;
      padding-top: 0.5rem;

      @include respond-to(sm) {
        font-size: 13px;
        padding-top: 0.625rem;
      }

      @include respond-to(md) {
        font-size: 14px;
        padding-top: 0.75rem;
      }

      @include respond-to(lg) {
        font-size: 15px;
        padding-top: 0.875rem;
      }

      @include respond-to(fhd) {
        font-size: 16px;
        padding-top: 1rem;
      }

      @include respond-to(qhd) {
        font-size: 18px;
        padding-top: 1.125rem;
      }

      @include respond-to(uhd) {
        font-size: 20px;
        padding-top: 1.25rem;
      }
    }

    .item-time {
      color: #6d7278;
      box-sizing: border-box;
      border-top: 1px solid #e5e5e5;
      display: flex;
      justify-content: space-between;
      align-items: center;

      // 响应式字体和间距
      font-size: 12px;
      padding-top: 0.75rem;
      margin-top: 1rem;

      @include respond-to(sm) {
        font-size: 13px;
        padding-top: 0.875rem;
        margin-top: 1.125rem;
      }

      @include respond-to(md) {
        font-size: 14px;
        padding-top: 1rem;
        margin-top: 1.25rem;
      }

      @include respond-to(lg) {
        font-size: 15px;
        padding-top: 1.125rem;
        margin-top: 1.375rem;
      }

      @include respond-to(fhd) {
        font-size: 16px;
        padding-top: 1.25rem;
        margin-top: 1.5rem;
      }

      @include respond-to(qhd) {
        font-size: 18px;
        padding-top: 1.375rem;
        margin-top: 1.75rem;
      }

      @include respond-to(uhd) {
        font-size: 20px;
        padding-top: 1.5rem;
        margin-top: 2rem;
      }

      .icon {
        font-size: 1.2em;
        opacity: 0.7;
        transition: opacity 0.3s ease;
      }

      &:hover .icon {
        opacity: 1;
      }
    }
  }
}

@import "./media.scss";
</style>
