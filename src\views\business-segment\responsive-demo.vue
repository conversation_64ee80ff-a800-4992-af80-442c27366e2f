<!--
 * 响应式演示页面
 * 展示优化后的business-details页面的响应式效果
 -->

<template>
  <div class="responsive-demo">
    <ResponsiveContainer type="container">
      <h1 class="demo-title">Business Details 响应式优化演示</h1>
      
      <!-- 设备信息 -->
      <div class="device-info">
        <h2>当前设备信息</h2>
        <div class="info-grid">
          <div class="info-item">
            <strong>窗口宽度:</strong> {{ windowWidth }}px
          </div>
          <div class="info-item">
            <strong>设备类型:</strong> {{ deviceType }}
          </div>
          <div class="info-item">
            <strong>当前断点:</strong> {{ currentBreakpoint }}
          </div>
        </div>
      </div>
      
      <!-- 面包屑演示 -->
      <div class="demo-section">
        <h3>面包屑导航 (响应式字体)</h3>
        <div class="breadcrumb-demo">
          <el-breadcrumb :separator-icon="ArrowRight">
            <el-breadcrumb-item>首页</el-breadcrumb-item>
            <el-breadcrumb-item>业务板块</el-breadcrumb-item>
            <el-breadcrumb-item>科研中心</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </div>
      
      <!-- 标题和描述演示 -->
      <div class="demo-section">
        <h3>标题和描述 (响应式字体和间距)</h3>
        <div class="title-demo">
          <div class="demo-title-large">科研中心标题</div>
          <div class="demo-desc">这是一个响应式的描述文本，会根据屏幕大小调整字体大小和行高。在移动端会显示较小的字体，在桌面端会显示较大的字体。</div>
        </div>
      </div>
      
      <!-- 图片和标签演示 -->
      <div class="demo-section">
        <h3>图片和标签布局 (响应式布局)</h3>
        <div class="img-demo">
          <div class="img-content">
            <div class="img-view">
              <div class="demo-image">演示图片区域</div>
            </div>
            <div class="sub-tag">
              <div class="sub-tag-item">
                <div class="sub-tag1">研发中心</div>
                <div class="sub-tag2">
                  <span class="location-icon">📍</span>
                  北京市海淀区
                </div>
              </div>
              <div class="sub-tag-item">
                <div class="sub-tag1">技术中心</div>
                <div class="sub-tag2">
                  <span class="location-icon">📍</span>
                  上海市浦东新区
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 按钮演示 -->
      <div class="demo-section">
        <h3>响应式按钮</h3>
        <div class="button-demo">
          <el-button class="btn-research">普通按钮</el-button>
          <el-button class="btn-active">激活按钮</el-button>
          <el-button class="btn-research">另一个按钮</el-button>
        </div>
      </div>
      
      <!-- 卡片网格演示 -->
      <div class="demo-section">
        <h3>响应式卡片网格</h3>
        <div class="cards-demo">
          <div class="card-item" v-for="i in 6" :key="i">
            <div class="demo-image">图片 {{ i }}</div>
            <div class="text-overlay">
              卡片 {{ i }} 的描述文本
              <div class="overlay-action">
                <span>查看更多</span>
                <el-icon><Right /></el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ResponsiveContainer>
  </div>
</template>

<script setup>
import { ArrowRight, Right } from "@element-plus/icons-vue"
import { useResponsive } from '@/hooks/useResponsive'
import ResponsiveContainer from '@/components/ResponsiveContainer/index.vue'

const {
  windowWidth,
  deviceType,
  currentBreakpoint
} = useResponsive()
</script>

<style lang="scss" scoped>
@import "@/styles/responsive-utilities.scss";

.responsive-demo {
  background: #f2f3f5;
  min-height: 100vh;
  padding: 2rem 0;
  
  .demo-title {
    @include responsive-font-size(24px, 28px, 32px);
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
  }
  
  .device-info {
    background: white;
    border-radius: 8px;
    @include responsive-spacing(padding, 1rem, 1.5rem, 2rem);
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    h2 {
      @include responsive-font-size(18px, 20px, 22px);
      margin-bottom: 1rem;
      color: #333;
    }
    
    .info-grid {
      @include responsive-grid(1, 2, 3);
      gap: 1rem;
      
      .info-item {
        background: #f8f9fa;
        @include responsive-spacing(padding, 0.75rem, 1rem, 1rem);
        border-radius: 6px;
        @include responsive-font-size(14px, 15px, 16px);
      }
    }
  }
  
  .demo-section {
    background: white;
    border-radius: 8px;
    @include responsive-spacing(padding, 1rem, 1.5rem, 2rem);
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    h3 {
      @include responsive-font-size(16px, 18px, 20px);
      margin-bottom: 1rem;
      color: #333;
      border-bottom: 2px solid #e9ecef;
      padding-bottom: 0.5rem;
    }
  }
  
  .breadcrumb-demo {
    :deep(.el-breadcrumb) {
      @include responsive-font-size(16px, 17px, 18px);
    }
  }
  
  .title-demo {
    .demo-title-large {
      @include responsive-font-size(24px, 28px, 32px);
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);
      line-height: 1.4;
      margin-bottom: 1rem;
    }
    
    .demo-desc {
      @include responsive-font-size(16px, 18px, 20px);
      color: #333;
      line-height: 1.6;
    }
  }
  
  .img-demo {
    .img-content {
      @include responsive-flex(column, row);
      align-items: flex-start;
      gap: 2rem;
      
      .img-view {
        flex: 1;
        
        .demo-image {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          @include responsive-spacing(padding, 3rem 2rem, 4rem 2rem, 5rem 2rem);
          text-align: center;
          border-radius: 8px;
          @include responsive-font-size(16px, 18px, 20px);
        }
      }
      
      .sub-tag {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        
        .sub-tag-item {
          .sub-tag1 {
            @include responsive-font-size(20px, 22px, 24px);
            color: #1f2a72;
            font-weight: 600;
            margin-bottom: 0.5rem;
          }
          
          .sub-tag2 {
            @include responsive-font-size(16px, 17px, 18px);
            color: #666;
            display: flex;
            align-items: center;
            gap: 0.5rem;
          }
        }
      }
    }
  }
  
  .button-demo {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    
    .btn-research {
      background-color: #f2f3f5;
      color: #333;
      border: 2px solid transparent;
      @include responsive-font-size(16px, 18px, 20px);
      @include responsive-spacing(padding, 0.5rem 1rem, 0.6rem 1.2rem, 0.7rem 1.5rem);
      transition: all 0.3s ease;
      
      &:hover {
        background-color: #078CEC;
        color: white;
        border-color: #078CEC;
        transform: translateY(-2px);
      }
    }
    
    .btn-active {
      background-color: #078CEC;
      color: white;
      border: 2px solid #078CEC;
      @include responsive-font-size(16px, 18px, 20px);
      @include responsive-spacing(padding, 0.5rem 1rem, 0.6rem 1.2rem, 0.7rem 1.5rem);
    }
  }
  
  .cards-demo {
    @include responsive-grid(1, 2, 3);
    gap: 1.5rem;
    
    .card-item {
      position: relative;
      border-radius: 12px;
      overflow: hidden;
      cursor: pointer;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      
      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }
      
      .demo-image {
        background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
        color: white;
        @include responsive-spacing(height, 200px, 220px, 250px);
        display: flex;
        align-items: center;
        justify-content: center;
        @include responsive-font-size(16px, 18px, 20px);
      }
      
      .text-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
        color: white;
        @include responsive-spacing(padding, 1.5rem 1rem 1rem, 2rem 1.5rem 1.5rem, 2.5rem 2rem 2rem);
        @include responsive-font-size(16px, 18px, 20px);
        
        .overlay-action {
          @include responsive-spacing(margin-top, 0.8rem, 1rem, 1.2rem);
          @include responsive-font-size(14px, 15px, 16px);
          display: flex;
          align-items: center;
          gap: 0.5rem;
          opacity: 0.9;
        }
      }
    }
  }
}
</style>
