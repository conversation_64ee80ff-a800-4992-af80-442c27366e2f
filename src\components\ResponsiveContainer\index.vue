<!--
 * 响应式容器组件
 * 提供统一的响应式布局容器
 -->

<template>
  <div 
    :class="containerClasses"
    :style="containerStyles"
  >
    <slot />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useResponsive } from '@/hooks/useResponsive'

const props = defineProps({
  // 容器类型
  type: {
    type: String,
    default: 'container', // container | fluid | section
    validator: (value) => ['container', 'fluid', 'section'].includes(value)
  },
  
  // 最大宽度配置
  maxWidth: {
    type: [String, Object],
    default: null
  },
  
  // 内边距配置
  padding: {
    type: [String, Object],
    default: null
  },
  
  // 是否居中
  centered: {
    type: Boolean,
    default: true
  },
  
  // 自定义类名
  customClass: {
    type: String,
    default: ''
  },
  
  // 响应式显示控制
  hideOn: {
    type: Array,
    default: () => []
  },
  
  // 响应式显示控制
  showOn: {
    type: Array,
    default: () => []
  }
})

const { 
  getResponsiveValue, 
  getContainerMaxWidth,
  getResponsiveSpacing,
  isMobileDevice,
  isTabletDevice,
  isDesktopDevice,
  currentBreakpoint
} = useResponsive()

// 容器类名
const containerClasses = computed(() => {
  const classes = ['responsive-container']
  
  // 基础类型
  classes.push(`responsive-container--${props.type}`)
  
  // 设备类型
  if (isMobileDevice.value) classes.push('responsive-container--mobile')
  if (isTabletDevice.value) classes.push('responsive-container--tablet')
  if (isDesktopDevice.value) classes.push('responsive-container--desktop')
  
  // 当前断点
  classes.push(`responsive-container--${currentBreakpoint.value}`)
  
  // 居中
  if (props.centered) classes.push('responsive-container--centered')
  
  // 自定义类名
  if (props.customClass) classes.push(props.customClass)
  
  // 响应式显示控制
  if (props.hideOn.includes(currentBreakpoint.value)) {
    classes.push('responsive-container--hidden')
  }
  
  if (props.showOn.length > 0 && !props.showOn.includes(currentBreakpoint.value)) {
    classes.push('responsive-container--hidden')
  }
  
  return classes.join(' ')
})

// 容器样式
const containerStyles = computed(() => {
  const styles = {}
  
  // 最大宽度
  if (props.maxWidth) {
    styles.maxWidth = getResponsiveValue(props.maxWidth)
  } else if (props.type === 'container') {
    styles.maxWidth = getContainerMaxWidth()
  }
  
  // 内边距
  if (props.padding) {
    const padding = getResponsiveSpacing({
      mobile: '1rem',
      tablet: '1.5rem',
      desktop: '2rem',
      ...props.padding
    })
    styles.padding = padding
  }
  
  return styles
})
</script>

<style lang="scss" scoped>
.responsive-container {
  width: 100%;
  
  &--centered {
    margin-left: auto;
    margin-right: auto;
  }
  
  &--container {
    padding-left: 1rem;
    padding-right: 1rem;
    
    @media screen and (min-width: 576px) {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
    
    @media screen and (min-width: 768px) {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }
  
  &--fluid {
    width: 100%;
    max-width: none;
  }
  
  &--section {
    padding-top: 2rem;
    padding-bottom: 2rem;
    
    @media screen and (min-width: 768px) {
      padding-top: 3rem;
      padding-bottom: 3rem;
    }
    
    @media screen and (min-width: 992px) {
      padding-top: 4rem;
      padding-bottom: 4rem;
    }
  }
  
  &--hidden {
    display: none !important;
  }
  
  // 设备特定样式
  &--mobile {
    // 移动端特定样式
  }
  
  &--tablet {
    // 平板端特定样式
  }
  
  &--desktop {
    // 桌面端特定样式
  }
}
</style>
