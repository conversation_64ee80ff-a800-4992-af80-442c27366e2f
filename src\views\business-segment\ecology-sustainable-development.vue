<!-- 
生态与可持续发展 页面 
-->
<template>
  <div class="business-details-main">
    <div class="sub-title">
      <div class="top-breadcrumb">
        <el-breadcrumb :separator-icon="ArrowRight">
          <el-breadcrumb-item :to="{ path: '/' }">{{ $t("menus.home") }}</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/business-segment' }">{{ $t("businessSegment.title") }}</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/business-segment/business-details' }">{{ $t("businessSegment.scienceCenter") }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t("scienceCenter.ecology_sustainalble") }}</el-breadcrumb-item>
          
        </el-breadcrumb>
      </div>
    </div>

    <div class="content-main">
      <div class="content">
        <!-- 生态与可持续发展 -->
        <div class="img-container1">
            <el-image :src="Ecology_sustainable_info.stykcxfzImg1" alt="生态与可持续发展图"> </el-image>
            <div class="text-overlay">
                <div class="title">{{ Ecology_sustainable_info[lang].title1 }}</div>
                <div class="desc" v-html="Ecology_sustainable_info[lang].desc1_1"></div>
            </div>
        </div>
        <!-- 核心概念 -->
        <div class="core-concepts-section">
            <div class="main-title">
               {{ Ecology_sustainable_info[lang].core_concepts }}
            </div>
            <div class="sub-title">
               {{ Ecology_sustainable_info[lang].c1 }}
            </div>
            <div class="description">
               {{ Ecology_sustainable_info[lang].c1_des }}
            </div>
        </div>
        <div style="margin: 0 auto;display: flex; flex-direction: row;">
          <div style="width: 25%;margin-left: 10%; margin-right: 0px;">
            <div style="text-align: right; font-size: 18px; font-weight: 600;margin-top: 12rem;">
               {{ Ecology_sustainable_info[lang].c2 }}
            </div>
            <div style="text-align:start;  font-size: 16px;font-weight: 400; line-height: 28px; margin-top: 4rem;">
               {{ Ecology_sustainable_info[lang].c2_des }}
            </div>
          </div>
          <div class="img-container1" style="width: auto;">
            <el-image :src="Ecology_sustainable_info.cycle_bg" alt=""> </el-image>
            <div class="text-overlay" style="height:332px;width: 332px; margin: 0 0;align-content: center;justify-content: center;">
                <div class="title" style="text-align: center; " v-html="Ecology_sustainable_info[lang].core_center"></div>               
            </div>
          </div>
          <div style="width: 25%; margin-right: 10%; padding-left: -100px; " >
            <div style="padding-left: -100px; text-align: left; font-size: 18px; font-weight: 600;margin-top: 12rem;">
               {{ Ecology_sustainable_info[lang].c3 }}
            </div>
            <div style="text-align:left; font-size: 16px; line-height: 28px; margin-top: 4rem;">
               {{ Ecology_sustainable_info[lang].c3_desc }}
            </div>
          </div>
        </div>
        <!-- 产业基地 -->
        <div style="text-align: center; font-size: 36px; margin-bottom: 1rem; margin-top: 189px;">
              {{ Ecology_sustainable_info[lang].industrial_base }}
        </div>
        <div style="text-align: center; font-size: 28px;">
              {{ Ecology_sustainable_info[lang].base_des }}
        </div>
        <div style="text-align: center;margin-top: 89px;">
          <el-image :src="Ecology_sustainable_info.base_map" :alt="Ecology_sustainable_info[lang].industrial_base"> </el-image>
        </div>
      </div>
    </div>
  </div>
</template>

<script  lang="ts" setup>
import { nextTick, ref } from "vue"
import { ArrowRight } from "@element-plus/icons-vue"
import { onBeforeMount, reactive } from "vue"

import ecology_sustainable_info from "./ecology-sustainable-info"


import { i18n } from "@/i18n"
import { watch } from "vue"

import { useRouter } from "vue-router"

const router = useRouter()
const Ecology_sustainable_info = reactive(ecology_sustainable_info)

const btnClick = (item) => {
  activeIndex.value = item.id;
  console.log(item.id,item.imgs);
  nextTick.apply();
}

const lang = ref(i18n.global.locale)

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  }
)

const imgClick = (path) =>{
  router.push({
    path: path,
    query: {
      // key: item.key
    }
  })
}
onBeforeMount(() => {
  console.log(ecology_sustainable_info)
})
</script>

<style lang="scss" scoped>
// 使用通用业务页面样式
.business-details-main {
  @extend .business-details-main;
  .sub-title {
    height: 100px;
    background: #f3f3f3;

    .top-breadcrumb {
      width: 1320px;
      max-width: 100%;
      margin: 0 auto;
      padding-top: 50px;

      color: rgba(0, 0, 0, 0.85);
      line-height: 25px;

      :deep(.el-breadcrumb) {
        font-size: 18px;

        &.el-breadcrumb__inner a:hover,
        .el-breadcrumb__inner.is-link:hover {
          color: inherit;
        }
      }
    }
  }

  .content-main {
    background: #f2f3f5;
  }

  .content {
    width: 80%;
    margin: 0 auto;
    padding: 50px 0;
    background-size: cover;

    .info {
      .title {
        margin-left: 10%;
        width: 100%;
        font-weight: 600;
        font-size: 26px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 49px;
        letter-spacing: 2px;
      }

      .desc {
        margin: 10px 10%;
        font-size: 20px;
        color: #000000;
        line-height: 28px;
        letter-spacing: 2px;
      }
    }
        .img-container1 {
            position: relative;
            display: flex;
            /* 启用flex布局 */
            justify-content: center;
            /* 水平居中 */
            align-items:start;
            /* 垂直居中 */
            width: 100%;
            color: white;
        }
        .text-overlay{
            position: absolute;
            z-index: 1; /* 确保文字在图片上方 */
            color: white;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5); /* 文字阴影增强可读性 */
            margin: 10% 20%;

            .title {
                font-size: 28px;
            }
            .desc {
                margin-top: 2rem;
                font-size: 12px;
                width: 50%;
            }
        }
    .img-view {
      margin: 10px 0;
      width: 100%;
      height: auto;

      .img {
        display: block;
        width: 100%;
        height: auto;
      }
    }
    .sub-tag {
      margin: 10px 0;
      display: flex;
      width: 100%;
      flex-direction: column;
    }
    .sub-tag1 {
      margin-top: 18px;
      color: blue;
      font-size: 24px;
      font-weight: 600;
    }
    .sub-tag2 {
      font-size: 18px;
      font-weight: 600;
      margin-top: 20px;
    }
    .imgpos {
      vertical-align: middle;
    }
  }
  .background-container {
    background-image: url(@/assets/business-segment/segment1-save.png);
    background-size: cover;
    background-attachment: fixed;
    width: 100%;
    height: auto; /* 视口高度 */
  }
  .research-institute {
    width: 1320px;
    max-width: 100%;
    margin: 0 auto;
    padding-top: 80px;
    padding-bottom: 80px;

    display: flex;
    flex-direction: column;
    gap: 60px;

    /* 跑马灯非激活的图片透明度 */
    :deep(.el-carousel__item:not(.is-active)) {
      opacity: 0.4;
    }
    /** 按钮标题 */
    .research-bt-div {
      // margin:0 auto;
      text-align: center;
    }
    .btn-research {
      background-color: #f2f3f5;
      color: black;
      margin: 0 1%;
      padding: 0.5rem 1rem;
      font-size: 20px;
      border: 1px solid #f2f3f5;
    }
    .btn-research :hover {
      background-color: #078CEC;      
      color: white;
      border: 1px solid #078CEC;
      border-radius: 1rem;
      padding: 0.5rem 1rem;
    }
    .btn-active {
      color: #078CEC;
    }
  .research-institute-imgs{
    :deep(.el-carousel__item) {
      padding: 0 8px; /* 左右各8px间距 */
    }
  }
   
    
  }
  

  // 核心概念区域样式
  .core-concepts-section {
    text-align: center;
    margin: 3rem auto;

    @include respond-to(md) {
      margin: 4rem auto;
    }

    @include respond-to(lg) {
      margin: 5rem auto;
    }

    @include respond-to(fhd) {
      margin: 6rem auto;
    }

    .main-title {
      font-weight: 700;
      color: rgba(0, 0, 0, 0.85);

      // 响应式字体大小
      font-size: 20px;
      margin-bottom: 1rem;

      @include respond-to(sm) {
        font-size: 22px;
        margin-bottom: 1.25rem;
      }

      @include respond-to(md) {
        font-size: 24px;
        margin-bottom: 1.5rem;
      }

      @include respond-to(lg) {
        font-size: 26px;
        margin-bottom: 1.75rem;
      }

      @include respond-to(fhd) {
        font-size: 28px;
        margin-bottom: 2rem;
      }

      @include respond-to(qhd) {
        font-size: 32px;
      }

      @include respond-to(uhd) {
        font-size: 38px;
      }
    }

    .sub-title {
      font-weight: 600;
      color: rgba(0, 0, 0, 0.75);

      // 响应式字体大小
      font-size: 16px;
      margin-bottom: 1.5rem;

      @include respond-to(sm) {
        font-size: 17px;
        margin-bottom: 1.75rem;
      }

      @include respond-to(md) {
        font-size: 18px;
        margin-bottom: 2rem;
      }

      @include respond-to(lg) {
        font-size: 19px;
      }

      @include respond-to(fhd) {
        font-size: 20px;
      }

      @include respond-to(qhd) {
        font-size: 22px;
      }

      @include respond-to(uhd) {
        font-size: 26px;
      }
    }

    .description {
      text-align: left;
      font-weight: 400;
      line-height: 1.6;
      color: rgba(0, 0, 0, 0.85);
      max-width: 800px;
      margin: 0 auto;

      // 响应式字体大小和内边距
      font-size: 14px;
      padding: 0 1rem;

      @include respond-to(sm) {
        font-size: 15px;
        padding: 0 1.5rem;
      }

      @include respond-to(md) {
        font-size: 16px;
        padding: 0 2rem;
        max-width: 900px;
      }

      @include respond-to(lg) {
        font-size: 17px;
        max-width: 1000px;
      }

      @include respond-to(fhd) {
        font-size: 18px;
        max-width: 1100px;
      }

      @include respond-to(qhd) {
        font-size: 20px;
        max-width: 1200px;
      }

      @include respond-to(uhd) {
        font-size: 24px;
        max-width: 1400px;
      }
    }
  }
}


@media (max-width: 1320px) {
  .business-details-main .sub-title .top-breadcrumb,
  .business-details-main .content {
    padding-left: 20px;
    padding-right: 20px;
  }

  .business-details-main .content .img-view {
    width: 100%;
    height: auto;
  }

  .business-details-main .research-institute {
    padding-left: 20px;
    padding-right: 20px;
    gap: 100px;
  }

  .business-details-main .research-institute .energy-security {
    flex-direction: column-reverse;
    gap: 30px;
    align-items: flex-start;
  }

  .business-details-main .research-institute .energy-security .left {
    padding: 0;
  }

  .business-details-main .research-institute .energy-security .right {
    left: 0;
    width: 100%;
    height: 350px;
  }

  .business-details-main .research-institute .energy-security:hover .left {
    background: initial;

    .title,
    .desc,
    .more-btn {
      color: initial;
    }

    .line {
      border: 1px solid #979797;
    }
  }

  .business-details-main .research-institute .energy-security .left .title {
    font-size: 34px;
  }
}

@media (max-width: 1024px) {
  .business-details-main .content .info .title {
    font-size: 28px;
    line-height: 38px;
    min-width: 300px;
  }
  .business-details-main .content .info .desc {
    font-size: 18px;
  }

  .business-details-main .research-institute .energy-security .left .title {
    font-size: 32px;
  }
}

@media (max-width: 820px) {
  .business-details-main .content .info {
    flex-direction: column;
    gap: 30px;
  }

  .business-details-main .sub-title .top-breadcrumb .el-breadcrumb {
    font-size: 18px;
  }

  .business-details-main .content .img-view {
    margin-top: 20px;
  }

  .business-details-main .research-institute .energy-security .left .title {
    font-size: 30px;
  }
  .business-details-main .research-institute .energy-security .left .line {
    margin-top: 30px;
    margin-bottom: 15px;
  }
}

@media (max-width: 768px) {
  .business-details-main .sub-title .top-breadcrumb .el-breadcrumb {
    font-size: 18px;
    line-height: 14px;
  }
}

@media (max-width: 576px) {
  .business-details-main .content .info .title {
    font-size: 24px;
    line-height: 34px;
  }

  .business-details-main .content .info .desc {
    font-size: 16px;
  }

  .business-details-main .content {
    padding-top: 20px;
    padding-bottom: 20px;
  }

  .business-details-main .research-institute .energy-security .right {
    width: 100%;
    min-width: 100%;
    height: auto;
  }

  .business-details-main .research-institute .energy-security .left .title {
    font-size: 26px;
  }
  .business-details-main .research-institute .energy-security .left .desc {
    font-size: 16px;
  }
  .business-details-main .research-institute .energy-security .left .more-btn {
    margin-top: 30px;
  }
  .business-details-main .content .img-container1 {
    margin: 5px 10px;
    display: grid;
    grid-template-columns: 90%;
  }
  .business-details-main .content .img-container1 .img-view {
    margin: 5px 10px;
  }
  .business-details-main .content .img-container1 .sub-tag1 {
    text-align: center;
  }
  .business-details-main .content .img-container1 .sub-tag2 {
    text-align: center;
  }
}

@media (max-width: 480px) {
  :deep(.el-carousel__arrow--right) {
    top: 13%;
  }

  .business-details-main .research-institute .energy-security .left .title {
    font-size: 24px;
  }
}

@media (max-width: 390px) {
  .business-details-main .content .info .title {
    font-size: 22px;
  }

  :deep(.el-carousel__indicators--horizontal) {
    display: flex;
    flex-wrap: nowrap;
  }
}
</style>
