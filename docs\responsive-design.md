# 响应式设计指南

## 概述

本项目采用移动优先的响应式设计策略，确保在各种设备和屏幕尺寸下都能提供良好的用户体验。

## 断点系统

### 标准断点

我们使用以下标准断点，与 Tailwind CSS 保持一致：

| 断点 | 最小宽度 | 设备类型 | 描述 |
|------|----------|----------|------|
| xs   | 480px    | 手机     | 超小屏幕 |
| sm   | 576px    | 大手机   | 小屏幕 |
| md   | 768px    | 平板     | 中等屏幕 |
| lg   | 992px    | 小桌面   | 大屏幕 |
| xl   | 1200px   | 桌面     | 超大屏幕 |
| 2xl  | 1400px   | 大桌面   | 超超大屏幕 |
| 3xl  | 1600px   | 超宽屏   | 超宽屏幕 |

### 设备类型判断

- **移动设备**: < 992px
- **平板设备**: 768px - 991px
- **桌面设备**: ≥ 992px

## 工具和资源

### 1. 响应式常量 (`src/constants/responsive.js`)

提供统一的断点定义和工具函数：

```javascript
import { BREAKPOINTS, isMobile, isTablet, isDesktop } from '@/constants/responsive'
```

### 2. 响应式 Hook (`src/hooks/useResponsive.js`)

提供响应式状态管理：

```javascript
import { useResponsive, useBreakpoint, useResponsiveValue } from '@/hooks/useResponsive'

const { 
  windowWidth, 
  deviceType, 
  isMobileDevice,
  getResponsiveValue 
} = useResponsive()
```

### 3. 响应式工具类 (`src/styles/responsive-utilities.scss`)

提供 SCSS mixins 和工具类：

```scss
@include respond-to(md) {
  // 中等屏幕及以上的样式
}

@include responsive-font-size(14px, 16px, 18px);
@include responsive-spacing(padding, 1rem, 1.5rem, 2rem);
```

### 4. 响应式容器组件 (`src/components/ResponsiveContainer`)

提供统一的响应式布局容器：

```vue
<ResponsiveContainer 
  type="container"
  :padding="{ mobile: '1rem', tablet: '1.5rem', desktop: '2rem' }"
>
  <!-- 内容 -->
</ResponsiveContainer>
```

## 使用指南

### 1. CSS 媒体查询

使用统一的断点值：

```scss
// 推荐：使用 mixin
@include respond-to(md) {
  font-size: 18px;
}

// 或直接使用媒体查询
@media screen and (min-width: 768px) {
  font-size: 18px;
}
```

### 2. JavaScript 响应式检测

```javascript
import { useResponsive } from '@/hooks/useResponsive'

const { isMobileDevice, currentBreakpoint } = useResponsive()

// 根据设备类型执行不同逻辑
if (isMobileDevice.value) {
  // 移动端逻辑
}
```

### 3. 响应式值计算

```javascript
import { useResponsiveValue } from '@/hooks/useResponsive'

const fontSize = useResponsiveValue({
  mobile: '14px',
  tablet: '16px',
  desktop: '18px'
})
```

### 4. Tailwind CSS 类名

使用响应式前缀：

```html
<div class="text-sm md:text-base lg:text-lg">
  响应式文本
</div>

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
  响应式网格
</div>
```

## 最佳实践

### 1. 移动优先

始终从移动端开始设计，然后逐步增强到更大的屏幕：

```scss
// 基础样式（移动端）
.component {
  font-size: 14px;
  padding: 1rem;
  
  // 平板及以上
  @include respond-to(md) {
    font-size: 16px;
    padding: 1.5rem;
  }
  
  // 桌面及以上
  @include respond-to(lg) {
    font-size: 18px;
    padding: 2rem;
  }
}
```

### 2. 性能优化

- 使用防抖处理窗口大小变化事件
- 避免在 resize 事件中执行重计算
- 使用 CSS 媒体查询而非 JavaScript 进行样式调整

### 3. 测试策略

- 在多种设备和屏幕尺寸下测试
- 使用浏览器开发者工具的设备模拟器
- 测试横屏和竖屏模式
- 验证触摸交互在移动设备上的表现

### 4. 内容策略

- 确保重要内容在小屏幕上可见
- 使用渐进式披露隐藏次要信息
- 优化图片和媒体文件的加载
- 考虑不同设备的输入方式（触摸 vs 鼠标）

## 组件响应式指南

### 导航组件

- 移动端使用汉堡菜单
- 桌面端显示完整导航
- 适当调整字体大小和间距

### 表格组件

- 移动端使用水平滚动
- 考虑使用卡片布局替代表格
- 隐藏非关键列

### 表单组件

- 移动端使用全宽输入框
- 调整标签和输入框的布局
- 优化触摸目标大小

### 图片和媒体

- 使用响应式图片
- 设置合适的宽高比
- 考虑不同设备的带宽限制

## 调试和测试

### 响应式测试页面

访问 `/responsive-test` 页面查看当前响应式状态和测试各种组件。

### 浏览器工具

1. 打开开发者工具
2. 切换到设备模拟器
3. 测试不同的设备预设
4. 手动调整窗口大小

### 常见问题

1. **断点不一致**: 确保使用统一的断点常量
2. **性能问题**: 避免频繁的 DOM 操作和重计算
3. **布局破坏**: 测试边界情况和极端屏幕尺寸
4. **触摸问题**: 确保触摸目标足够大（至少 44px）

## 更新日志

- 2024-12-25: 统一响应式断点系统
- 2024-12-25: 添加响应式 Hook 和工具类
- 2024-12-25: 优化主要组件的响应式设计
- 2024-12-25: 创建响应式测试页面
