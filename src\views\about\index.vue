<!--
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-11-04 18:09:37
-->

<template>
  <div class="about-main">
    <div class="background-img">
      <p class="bg-desc">{{ $t("aboutUs.title") }}</p>
    </div>

    <div class="content">
      <div class="sticky-header">
        <div class="card">
          <div class="title">
            <div :class="{ active: activeAboutDom == 'part1' }" @click="gotoDomView('part1')">
              {{ $t("aboutUs.companyProfile") }}
            </div>
            <div :class="{ active: activeAboutDom == 'part5' }" @click="gotoDomView('part5')">
              {{ $t("aboutUs.coreAreas") }}
            </div>
            <div :class="{ active: activeAboutDom == 'part2' }" @click="gotoDomVie<PERSON>('part2')">
              {{ $t("aboutUs.visionAndCulture") }}
            </div>
            <div :class="{ active: activeAboutDom == 'part3' }" @click="gotoDomView('part3')">
              {{ $t("aboutUs.companyCulture") }}
            </div>
            <div :class="{ active: activeAboutDom == 'part4' }" @click="gotoDomView('part4')">
              {{ $t("aboutUs.chairmanMessage") }}
            </div>

            <!-- <div :class="{ active: activeAboutDom == 'part5' }" @click="gotoDomView('part5')">
              {{ $t("aboutUs.threeAreasTitle") }}
            </div>
            <div :class="{ active: activeAboutDom == 'part6' }" @click="gotoDomView('part6')">
              {{ $t("aboutUs.threePlates") }}
            </div> -->

            <div :class="{ active: activeAboutDom == 'part7' }" @click="gotoDomView('part7')">
              {{ $t("aboutUs.organizationalStructure") }}
            </div>
          </div>
        </div>
      </div>

      <div class="cards-main">
        <div class="profile" id="part1">
          <div class="profile-main">
            <div class="left">
              <el-image class="img" :src="aboutDesc" fit="cover" lazy />
            </div>
            <div class="right">
              <p class="title">{{ $t("aboutUs.companyProfile") }}</p>
              <p class="desc" v-html="$t('aboutUs.companyProfile_desc')"></p>
            </div>
          </div>
        </div>

        <div class="profile" id="part5" style="background-color: #f3f3f3 ;">
          <div style="margin: 0 auto; text-align: center; font-size: 36px; line-height: 50px;">{{
            $t("aboutUs.coreAreas") }}</div>
          <div style="margin: 0 auto; max-width: 640px; width: 492px;">
            
            <el-tabs v-model="activeName" class="tabs-coreArea" @tab-click="handleClick">
              <el-tab-pane :label="coreAreaInfo[lang].children[0].title" name="0">
              </el-tab-pane>
              <el-tab-pane :label="coreAreaInfo[lang].children[1].title" name="1">
              </el-tab-pane>
              <el-tab-pane :label="coreAreaInfo[lang].children[2].title" name="2">
              </el-tab-pane>
            </el-tabs>
          
          </div>
          <div style="margin: 0 auto; width: 100%; max-width: 1920px; text-align: center;">
            <el-carousel type="card" height="auto" :style="`height: ${itemStyleHeight}px`" cardScale="1" arrow="always">
              <el-carousel-item v-for="item in coreAreaInfo[lang].children" :key="item" :style="`height: ${itemStyleHeight}px`">
                <div class="img-container1">
                  <el-image :src="item.img" style="width: 100%;" fit="cover" lazy></el-image>
                  <div class="text-overlay">
                    <div class="title" v-html="item.title"></div>
                    <div class="desc" v-html="item.desc"></div>
                    <div style="bottom: 20px; padding-top: 20px;font-size: 16px;"> <span>{{ item.more }}</span><el-icon>
                        <Right />
                      </el-icon></div>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
            <div style="padding-top:54px; padding-bottom: 221px; font-size: 24px; line-height: 33px;">{{coreAreaInfo[lang].more}}</div>
          </div>
        </div>

        <!-- 科研中心 -->
        <div id="part6" style="margin: 0 auto; max-width: 1920px;">
          <div style="margin:0 11.62%; display: flex; flex-direction: row;">
            <div>
              <el-image :src="resear_centor"></el-image>
            </div>
            <div style="margin-top: 118px; margin-left: 104px;">
              <div style="font-size: 36px;" v-html="businessSegmentInfo[lang].title"></div>
              <!-- <div style="font-size: 24px;" v-html="businessSegmentInfo[lang].homeDesc"></div> -->
              <div>
                <div style="font-size: 24px;">{{ businessSegmentInfo[lang].center1 }}</div>
                <div style="font-size: 20px;">
                  <el-image class="imgpos" :src="businessSegmentInfo.position_img" lazy />{{
                  businessSegmentInfo[lang].center1_pos
                  }}
                </div>
                <div style="font-size: 24px; margin-top: 42px;">{{ businessSegmentInfo[lang].center2 }}</div>
                <div style="font-size: 20px;">
                  <el-image class="imgpos" :src="businessSegmentInfo.position_img" lazy />{{
                  businessSegmentInfo[lang].center2_pos
                  }}
                </div>
              </div>
            </div>
          </div>
          <div style="text-align: center;margin-top: 88px;margin-bottom: 97px;font-size: 18px;line-height: 25px;">
            {{ businessSegmentInfo[lang].desc }}
          </div>
        </div>

        <div class="science-park" style="display: flex; text-align: center; ">
          <p class="science-desc" style="font-size: 48px; margin: 0 auto;">{{ $t("aboutUs.beGoodLifeProvider") }}</p>          
        </div>
        <div style="float:none ; margin: -350px auto 0 auto;  width: 70%; padding-top: 0px; z-index: 1;" id="part2">
          <div style="background: blue; opacity: 0.7;color: white;">
            <div style="font-size: 36px; line-height: 52px; margin: 80px 200px; padding-top: 40px;">{{ $t("aboutUs.visionAndCulture") }}</div>
            <div style="font-size: 18px; line-height: 24px; margin: 80px 200px; padding-bottom: 100px;" v-html="$t('aboutUs.companyVision_desc1')"></div>
          </div>
        </div>
        <!-- <div class="corporate-vision" id="part2">
          <div class="vision-main">
            <div class="title">{{ $t("aboutUs.visionAndCulture") }}</div>
            <div class="desc" v-html="$t('aboutUs.companyVision_desc1')"></div>
          </div>
        </div> -->

        <div class="culture" id="part3">
          <div class="culture-main">
            <p class="culture-title">{{ $t("aboutUs.companyCulture") }}</p>
            <div class="CultureIMgBox">
              <div class="culture-view">
                <div class="culture-item">
                  <el-image class="img" :src="unity" fit="cover" lazy />
                  <div class="txt">{{ $t("aboutUs.companyCulture_desc1") }}</div>
                </div>
                <div class="culture-item">
                  <el-image class="img" :src="integrity" fit="cover" lazy />
                  <div class="txt">{{ $t("aboutUs.companyCulture_desc5") }}</div>
                </div>
                <div class="culture-item">
                  <el-image class="img" :src="modest" fit="cover" lazy />
                  <div class="txt">{{ $t("aboutUs.companyCulture_desc4") }}</div>
                </div>
                <div class="culture-item">
                  <el-image class="img" :src="profession" fit="cover" lazy />
                  <div class="txt">{{ $t("aboutUs.companyCulture_desc6") }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="chairman-message" id="part4">
          <div class="message-main">
            <p class="title">{{ $t("aboutUs.chairmanMessage") }}</p>
            <div class="desc">
              <p v-html="$t('aboutUs.chairmanMessage_desc1')"></p>
              <p class="third" v-html="$t('aboutUs.chairmanMessage_desc2')"></p>
            </div>
          </div>
        </div>

        

        <!-- <div class="core-area" id="part5">
          <div class="core-area-main">
            <div class="title">{{ $t("aboutUs.threeAreas") }}</div>
            <div class="core-area-content">
              <div class="view-main">
                <div class="item">{{ $t("aboutUs.threeAreasItem1") }}</div>
                <div class="item">{{ $t("aboutUs.threeAreasItem2") }}</div>
                <div class="item" v-html="$t('aboutUs.threeAreasItem3')"></div>
              </div>
            </div>
          </div>
        </div> -->

        <!-- <div class="enterprise-architecture" id="part6">
          <div class="architecture-main">
            <p class="title">{{ $t("aboutUs.threePlates") }}</p>
            <p class="desc">{{ $t("aboutUs.threePlates_desc") }}</p>

            <div>
              <el-image class="img" :src="mapAboutArchitecture[lang]" fit="cover" lazy />
            </div>
          </div>
        </div> -->

        <div class="organizational-structure" id="part7">
          <div class="structure-main">
            <div class="item-title">{{ $t("aboutUs.organizationalStructure") }}</div>
            <div class="img-view">
              <el-image class="img" :src="mapStructure[lang]" fit="cover" lazy />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onBeforeMount, onMounted, onBeforeUnmount, ref, watch, reactive } from "vue"
import { useRouter } from "vue-router"

import aboutDesc from "@/assets/about-us/about-desc.png"

import aboutArchitecture_ch from "@/assets/about-us/aboutArchitecture_ch.jpg"
import aboutArchitecture_en from "@/assets/about-us/aboutArchitecture_en.jpg"
import aboutArchitecture_hk from "@/assets/about-us/aboutArchitecture_hk.jpg"
import segment1_save from "@/assets/business-segment/segment1-save.png"

import structure_zh from "@/assets/about-us/structure_zh.png"
import structure_en from "@/assets/about-us/structure_en.png"
import structure_hk from "@/assets/about-us/structure_hk.png"

import integrity from "@/assets/about-us/integrity.jpg"
import profession from "@/assets/about-us/profession.jpg"
import unity from "@/assets/about-us/unity.jpg"
// import smile from "@/assets/about-us/smile.png"
// import friendly from "@/assets/about-us/friendly.png"
import modest from "@/assets/about-us/modest.jpg"
import coreArea_Info from "./coreArea_Info"
import { i18n } from "@/i18n"
import BusinessSegmentList from "@/views/business-segment/business-segment-list"
import resear_centor from "@/assets/about-us/resear-centor.png"

const router = useRouter()
const itemStyleHeight = ref(0)
const activeAboutDom = ref("part1")
const coreAreaInfo = reactive(coreArea_Info)
const lang = ref(i18n.global.locale)

let businessSegmentInfo = reactive({
  key: "",
  title: "",
  desc: "",
  img: "",
  position_img: ""
})

const updateBusinessSegmentInfo = (key) => {
  const foundSegment = BusinessSegmentList.find((item) => item.key === key)
  if (foundSegment) {
    Object.assign(businessSegmentInfo, foundSegment)
  }
  // let maxHeight = 0
  // const imgItems = document.querySelectorAll(".img-container1")
  //   imgItems.forEach((item) => {
  //     console.log(item);
  //     maxHeight = Math.max(maxHeight, item.clientHeight)
  //   });
    itemStyleHeight.value = 521/1920 * screen.width;
}

const gotoDomView = (id) => {
  localStorage.setItem("activeAboutDom", id)
  scrollPage(id)
}

const scrollPage = (part) => {
  if (part) {
    activeAboutDom.value = part

    const appScrollbar = document.getElementsByClassName("app-scrollbar")[0]
    appScrollbar.removeEventListener("scroll", () => {})

    const dom = document.getElementById(part)
    dom.scrollIntoView({ behavior: "smooth" })
  }
}

const mapAboutArchitecture = {
  zh: aboutArchitecture_ch,
  en: aboutArchitecture_ch,
  zh_hk: aboutArchitecture_ch
}

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  }
)

const mapStructure = {
  zh: structure_zh,
  en: structure_en,
  zh_hk: structure_hk
}

watch(
  () => router.currentRoute.value.query?.part,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      scrollPage(newVal)
    }
  }
)

onBeforeMount(() => {
  updateBusinessSegmentInfo("AcademyOfSciences");
  const part = localStorage.getItem("activeAboutDom")
  if (part) {
    router.push({ query: { part: part } })
  }
})

onMounted(() => {
  scrollPage(router.currentRoute.value.query?.part)

  localStorage.setItem("activeAboutDom", activeAboutDom.value)
})

onBeforeUnmount(() => {
  const part = localStorage.getItem("activeAboutDom")
  if (part) {
    localStorage.removeItem("activeAboutDom")
  }
})
</script>

<style lang="scss" scoped>
.about-main {
  background: #f2f3f5;
  width: 100%;

  .background-img {
    width: 100%;
    background: url("@/assets/about-us/about-bg.jpg") no-repeat center center;
    background-size: cover;
    position: relative;

    // 响应式高度
    height: 250px;

    @include respond-to(sm) {
      height: 300px;
    }

    @include respond-to(md) {
      height: 350px;
    }

    @include respond-to(lg) {
      height: 400px;
    }

    @include respond-to(xl) {
      height: 450px;
    }

    @include respond-to(fhd) {
      height: 490px;
    }

    @include respond-to(qhd) {
      height: 550px;
    }

    @include respond-to(uhd) {
      height: 650px;
    }
  }

  .bg-desc {
    @extend .responsive-container;
    font-weight: 700;
    color: #ffffff;
    text-align: left;
    font-style: normal;
    text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);

    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%);

    // 响应式字体大小和字间距
    font-size: 20px;
    line-height: 1.4;
    letter-spacing: 2px;

    @include respond-to(sm) {
      font-size: 24px;
      letter-spacing: 3px;
    }

    @include respond-to(md) {
      font-size: 28px;
      letter-spacing: 3px;
    }

    @include respond-to(lg) {
      font-size: 32px;
      letter-spacing: 4px;
    }

    @include respond-to(xl) {
      font-size: 35px;
      letter-spacing: 4px;
    }

    @include respond-to(fhd) {
      font-size: 38px;
      letter-spacing: 5px;
    }

    @include respond-to(qhd) {
      font-size: 42px;
      letter-spacing: 5px;
    }

    @include respond-to(uhd) {
      font-size: 50px;
      letter-spacing: 6px;
    }
  }

  .content {
    background: #f3f3f3;

    .sticky-header {
      background-color: #f3f3f3;
      position: sticky;
      top: 0px;
      z-index: 1000;

      overflow: hidden;
    }

    .card {
      @extend .responsive-container;

      // 响应式字体大小和内边距
      font-size: 14px;
      padding-top: 2rem;
      padding-bottom: 1rem;

      @include respond-to(sm) {
        font-size: 15px;
        padding-top: 2.5rem;
      }

      @include respond-to(md) {
        font-size: 16px;
        padding-top: 3rem;
        padding-bottom: 1.25rem;
      }

      @include respond-to(lg) {
        font-size: 17px;
        padding-top: 3.5rem;
      }

      @include respond-to(xl) {
        font-size: 18px;
        padding-top: 4rem;
        padding-bottom: 1.5rem;
      }

      @include respond-to(fhd) {
        font-size: 20px;
        padding-top: 4.5rem;
      }

      @include respond-to(qhd) {
        font-size: 22px;
        padding-top: 5rem;
      }

      @include respond-to(uhd) {
        font-size: 26px;
        padding-top: 6rem;
        padding-bottom: 2rem;
      }
    }

    .title {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;

      // 响应式间距
      gap: 1rem;

      @include respond-to(sm) {
        gap: 1.5rem;
      }

      @include respond-to(md) {
        gap: 2rem;
      }

      @include respond-to(lg) {
        gap: 3rem;
        flex-wrap: nowrap;
      }

      @include respond-to(xl) {
        gap: 4rem;
      }

      @include respond-to(fhd) {
        gap: 5rem;
      }

      @include respond-to(qhd) {
        gap: 6rem;
      }

      @include respond-to(uhd) {
        gap: 8rem;
      }

      div {
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;

        // 响应式字体大小和内边距
        font-size: 12px;
        padding-bottom: 0.5rem;

        @include respond-to(sm) {
          font-size: 13px;
          padding-bottom: 0.625rem;
        }

        @include respond-to(md) {
          font-size: 14px;
          padding-bottom: 0.75rem;
        }

        @include respond-to(lg) {
          font-size: 15px;
        }

        @include respond-to(xl) {
          font-size: 16px;
          padding-bottom: 1rem;
        }

        @include respond-to(fhd) {
          font-size: 18px;
        }

        @include respond-to(qhd) {
          font-size: 20px;
        }

        @include respond-to(uhd) {
          font-size: 24px;
          padding-bottom: 1.25rem;
        }

        &:hover {
          color: #409eff;
        }
      }

      .active {
        font-weight: 700;
        border-radius: 2px;

        transition: all 0.3s;

        &::before {
          height: 4px;
          content: "";
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
          background: #1c2a77;
          border-radius: 2px;
          transition: move 0.3s ease;

          @keyframes move {
            0% {
              transform: translateX(-20px);
              opacity: 0;
            }
            100% {
              transform: translateX(0);
              opacity: 1;
            }
          }
        }
      }
    }
  }

  .profile {
    background: #ffffff;

    .profile-main {
      max-width: 1320px;
      width: 100%;
      margin: 0 auto;
      display: flex;
      gap: 102px;
      padding-top: 122px;
      padding-bottom: 122px;

      .right {
        width: 460px;
        padding-top: 89px;
        .title {
          font-weight: 500;
          font-size: 36px;
          color: rgba(0, 0, 0, 0.85);
          line-height: 50px;
          letter-spacing: 3px;
          text-align: left;
        }

        .desc {
          font-size: 18px;
          color: #000000;
          line-height: 28px;
          text-align: left;
          padding-top: 36px;
        }
      }

      .left {
        flex: 1;
        border-radius: 8px;
        overflow: hidden;

        .img {
          display: block;
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .corporate-vision {
    background: #f3f3f3;

    .vision-main {
      max-width: 1060px;
      width: 100%;
      margin: 0 auto;
      display: flex;
      padding-top: 120px;
      padding-bottom: 120px;
      align-items: center;
    }

    .title {
      width: 400px;
      font-weight: 500;
      font-size: 36px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 50px;
      font-style: normal;
    }

    .desc {
      width: 100%;
      font-size: 18px;
      color: #000000;
      line-height: 28px;
      // letter-spacing: 1px;
      font-style: normal;
      padding-left: 120px;
      // border-left: 1px solid rgba(0, 0, 0, 0.5);
    }
  }

  .culture {
    background: #ffffff;

    .culture-main {
      max-width: 1320px;
      width: 100%;
      margin: 0 auto;
      padding-top: 120px;
      padding-bottom: 120px;
    }

    .culture-title {
      font-weight: 500;
      font-size: 36px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 50px;
      letter-spacing: 3px;
      text-align: center;
    }
    .CultureIMgBox {
      margin-top: 60px;

      .culture-view {
        width: 1320px;

        display: flex;
        justify-content: space-between;
      }

      .txt {
        font-size: 24px;
        color: #000000;
        text-align: center;
        padding-top: 28px;
      }
    }
  }

  .chairman-message {
    background: #f3f3f3;

    .message-main {
      max-width: 1060px;
      width: 100%;
      margin: 0 auto;
      padding-top: 107px;
      padding-bottom: 52px;
    }

    .title {
      font-size: 36px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 50px;
      letter-spacing: 3px;
      text-align: left;
      font-style: normal;
    }

    .desc {
      font-size: 18px;
      color: #000000;
      line-height: 28px;
      letter-spacing: 2px;
      font-style: normal;
      padding-top: 66px;
    }

    .third {
      padding-top: 40px;
      padding-bottom: 106px;
    }

    .desc1_sub1,
    .strong {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      // letter-spacing: 2px;
      text-align: justify;
      line-height: 28px;
      font-weight: 700;
    }
  }

  .science-park {
    width: 100%;
    height: 600px;
    background: url("@/assets/home/<USER>") no-repeat center center;
    background-size: cover;

    .science-desc {
      font-weight: 500;
      font-size: 48px;
      color: #ffffff;
      line-height: 52px;
      letter-spacing: 3px;
      text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
      text-align: center;
      font-style: normal;
      padding-top: 87px;
    }
  }

  .core-area {
    background: #f0f2f5;

    .core-area-main {
      max-width: 1060px;
      width: 100%;
      margin: 0 auto;
      padding-top: 90px;
      padding-bottom: 100px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .title {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 36px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 50px;
        letter-spacing: 3px;
        text-align: center;
        font-style: normal;
      }

      .core-area-content {
        display: flex;
        justify-content: center;
        margin-top: 37px;

        .view-main {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-wrap: wrap;

          position: relative;
          height: 350px;
          width: 350px;

          .item {
            position: absolute;
            font-family: SourceHanSansCN, SourceHanSansCN;
            font-weight: 800;
            font-size: 18px;
            color: #ffffff;
            line-height: 27px;
            letter-spacing: 2px;
            text-align: center;
            font-style: normal;

            width: 185px;
            height: 185px;
            display: flex;
            justify-content: center;
            align-items: center;

            clip-path: polygon(50% 0%, 90% 20%, 90% 80%, 50% 100%, 10% 80%, 10% 20%);
          }

          .item:nth-child(1) {
            background: #078cec;
            top: 0;
          }

          .item:nth-child(2) {
            background: #8dd4ff;
            top: 148px;
            right: 156px;
          }

          .item:nth-child(3) {
            background: #4dbb30;
            top: 148px;
            left: 156px;
          }
        }
      }
    }
  }

  .enterprise-architecture {
    background: #f3f3f3;

    .architecture-main {
      max-width: 1060px;
      width: 100%;
      margin: 0 auto;
      padding-top: 118px;
      padding-bottom: 200px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .title {
        font-weight: 500;
        font-size: 36px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 50px;
        letter-spacing: 6px;
        text-align: center;
      }

      .desc {
        font-size: 18px;
        color: #000000;
        padding-top: 56px;
      }

      .img {
        padding-top: 50px;

        display: block;
        width: 100%;
        height: 100%;
      }
    }
  }

  .organizational-structure {
    background: #ffffff;

    .structure-main {
      max-width: 1060px;
      width: 100%;
      margin: 0 auto;

      padding: 150px 0;

      display: flex;
      flex-direction: column;
      align-items: center;

      .item-title {
        font-weight: 500;
        font-size: 36px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 50px;
        letter-spacing: 6px;
        text-align: center;
      }

      .img-view {
        padding-top: 100px;
      }
    }
  }
  .tabs-coreArea  {
    margin-top: 97px;
    :deep(.el-tabs__item.is-active, .el-tabs__item:hover) {
      color: black;
    }
    :deep(.el-tabs__item) {
      font-size: 24px;
      line-height: 36px;
      margin-bottom: 20px;
      color: black;
      
    }
    :deep(.el-tabs__active-bar) {
      background-color: black;
    }
  }

  .img-container1 {
      position: relative;
      display: flex;
      /* 启用flex布局 */
      justify-content: start;
      /* 水平居中 */
      align-items: end;
      /* 垂直居中 */
      width: 100%;
      color: white;
      // background-color: #f3f3f3 ;
  }
  
  .text-overlay {
      position: absolute;
      z-index: 1;
      /* 确保文字在图片上方 */
      
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
      /* 文字阴影增强可读性 */
      margin-left: 36px;
      margin-bottom: 1rem;
  
      .title {
        font-size: 36px;
        line-height: 50px;
      }
  
      .desc {
        margin-top: 26px;
        line-height: 22px;
        font-size: 16px;
        width: 100%;
        letter-spacing: 1.33px;
      }
  }
  :deep(.el-carousel__item:not(.is-active)) {
    opacity: 0.4;
  }

}

#part6 .profile .content {
    width: 80%;
    margin: 0 auto;
    padding: 50px 0;
    background-image: url(@/assets/business-segment/segment1bg.png);
    background-size: cover;

    .info {
      .title {
        margin-left: 10%;
        width: 100%;
        font-weight: 600;
        font-size: 26px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 49px;
        letter-spacing: 2px;
      }

      .desc {
        margin: 10px 10%;
        font-size: 20px;
        color: #000000;
        line-height: 28px;
        letter-spacing: 2px;
      }
    }
    .img-container1 {
      display: grid;
      width: 100%;
      grid-template-columns: 70% 30%;
      height: auto;
    }
    .img-view {
      margin: 10px 0;
      width: 100%;
      height: auto;

      .img {
        display: block;
        width: 100%;
        height: auto;
      }
    }
    .sub-tag {
      margin: 10px 0;
      display: flex;
      width: 100%;
      flex-direction: column;
    }
    .sub-tag1 {
      margin-top: 18px;
      color: blue;
      font-size: 24px;
      font-weight: 600;
    }
    .sub-tag2 {
      font-size: 18px;
      font-weight: 600;
      margin-top: 20px;
    }
    .imgpos {
      vertical-align: middle;
    }
  }

@media (max-width: 1320px) {
  .about-main .bg-desc,
  .about-main .content .card,
  .about-main .profile .profile-main,
  .about-main .corporate-vision .vision-main,
  .about-main .culture .culture-main,
  .about-main .chairman-message .message-main,
  .about-main .core-area .core-area-main,
  .about-main .organizational-structure .structure-main,
  .about-main .enterprise-architecture .architecture-main {
    padding-left: 20px;
    padding-right: 20px;
  }

  .about-main .profile .profile-main {
    gap: 100px;
  }

  .about-main .culture .culture-main {
    gap: 200px;
  }

  .about-main .culture .title {
    font-size: 22px;
  }

  .about-main .culture .desc {
    font-size: 22px;
  }

  .about-main .culture .CultureIMgBox .culture-view {
    width: 100%;
  }
}

@media (max-width: 1200px) {
  .about-main .culture .culture-main {
    gap: 30px;
  }

  .about-main .culture .left .img {
    width: 80%;
    height: 100%;
  }

  .about-main .culture .title {
    font-size: 22px;
  }

  .about-main .culture .desc {
    font-size: 22px;
  }
}

@media (max-width: 1024px) {
  .about-main .profile .profile-main .right .title,
  .about-main .corporate-vision .title,
  .about-main .culture .culture-title,
  .about-main .organizational-structure .structure-main .item-title,
  .about-main .chairman-message .title,
  .about-main .enterprise-architecture .architecture-main .title {
    font-size: 30px;
  }
  .about-main .profile .profile-main .right {
    padding-top: 60px;
  }
  .about-main .corporate-vision .title {
    width: 280px;
  }

  .about-main .profile .profile-main .left {
    width: 350px;
  }

  .about-main .profile .profile-main {
    gap: 80px;
  }

  .about-main .culture .left {
    width: 500px;
  }

  .about-main .culture .CultureIMgBox .culture-view .culture-item .txt {
    font-size: 24px;
    padding-top: 15px;
  }
}

@media (max-width: 900px) {
  .culture-main {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .about-main .bg-desc {
    font-size: 32px;
  }

  .culture-main .left,
  .culture-main .right {
    width: 60%;
  }

  .culture-main .left .img {
    display: block;
    width: 100%;
    height: 100%;
  }

  .about-main .culture .desc {
    display: flex;
    gap: 30px;
  }

  .about-main .background-img {
    height: 400px;
  }

  .about-main .culture .left {
    width: 600px;
  }
}

@media (max-width: 820px) {
  .about-main .corporate-vision .title {
    width: 220px;
  }

  .about-main .bg-desc {
    font-size: 30px;
  }

  .about-main .content .title {
    gap: 50px;
  }

  .about-main .profile .profile-main .right .title,
  .about-main .corporate-vision .title,
  .about-main .culture .culture-title,
  .about-main .organizational-structure .structure-main .item-title,
  .about-main .chairman-message .title,
  .about-main .enterprise-architecture .architecture-main .title {
    font-size: 28px;
  }

  .about-main .corporate-vision .desc {
    padding-left: 80px;
  }

  .about-main .profile .profile-main .left {
    width: 300px;
  }

  .about-main .profile .profile-main {
    gap: 40px;
  }

  .about-main .background-img {
    height: 300px;
  }

  .about-main .culture .left {
    width: 550px;
  }
}

@media (max-width: 768px) {
  .about-main .profile .profile-main {
    flex-direction: column;
  }

  .about-main .profile .profile-main .right {
    padding-top: 10px;
  }

  .about-main .profile .profile-main .right,
  .about-main .profile .profile-main .left {
    width: 100%;
  }

  .about-main .corporate-vision .vision-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 30px;
  }

  .about-main .culture .CultureIMgBox .culture-view {
    flex-direction: column;
  }

  .about-main .corporate-vision .desc {
    border: none;
    padding-left: 0px;
  }

  .about-main .culture .culture-main {
    gap: 60px;
  }

  .about-main .culture .left {
    width: 500px;
  }

  .about-main .culture .desc {
    padding-top: 30px;
  }
  .about-main .corporate-vision .desc {
    margin: 0 auto;
  }

  .about-main .profile .profile-main .right .title {
    text-align: center;
  }
  .about-main .content .title {
    justify-content: center;
  }

  .about-main .corporate-vision .title {
    margin: 0 auto;
    letter-spacing: 3px;
  }

  .about-main .culture .culture-main {
    padding-top: 60px;
    padding-bottom: 60px;
  }
  .about-main .culture .culture-main {
    gap: 0;
  }

  .about-main .chairman-message .title {
    //width: 23%;
    margin: 0 auto;
  }
}

@media (max-width: 576px) {
  .about-main .bg-desc {
    font-size: 28px;
  }

  .about-main .science-park .science-desc {
    padding-left: 20px;
    padding-right: 20px;
  }

  .about-main .content .title {
    gap: 30px;
  }

  .about-main .culture .left {
    width: 250px;
  }

  .about-main .culture .desc {
    padding-top: 20px;
    line-height: 45px;
  }

  .about-main .culture .culture-main {
    gap: 30px;
  }

  .about-main .culture .left {
    width: 380px;
  }

  .about-main .profile .profile-main .right .title,
  .about-main .corporate-vision .title,
  .about-main .culture .culture-title,
  .about-main .organizational-structure .structure-main .item-title,
  .about-main .chairman-message .title,
  .about-main .enterprise-architecture .architecture-main .title {
    font-size: 22px;
  }

  .about-main .culture .desc {
    font-size: 20px;
  }

  .about-main .culture .CultureIMgBox {
    margin-top: 10px;
  }

  .about-main .chairman-message .message-main {
    padding-top: 60px;
  }
  .about-main .chairman-message .desc {
    padding-top: 30px;
  }
  .about-main .chairman-message .third {
    padding-bottom: 0;
  }

  .about-main .science-park .science-desc {
    font-size: 16px;
    line-height: 24px;
    padding-top: 35px;
  }
  .about-main .science-park {
    height: 400px;
  }
}

@media (max-width: 480px) {
  .about-main .bg-desc {
    font-size: 24px;
  }

  .about-main .content .title {
    gap: 15px;
  }

  .about-main .culture .culture-main {
    flex-direction: column;
  }

  .about-main .culture .left {
    width: 350px;
  }

  .about-main .culture .title {
    font-size: 18px;
  }

  .about-main .culture .desc {
    font-size: 18px;
  }

  .about-main .culture .right {
    .desc {
      display: flex;
      gap: 20px;
    }
  }

  .about-main .profile .profile-main .left .title,
  .about-main .culture .title,
  .about-main .chairman-message .title,
  .about-main .enterprise-architecture .architecture-main .title,
  .about-main .culture .desc,
  .about-main .corporate-vision .title,
  .about-main .core-area .core-area-main .title {
    font-size: 22px;
    line-height: 30px;
  }

  .about-main .profile .profile-main .right .desc,
  .about-main .corporate-vision .desc,
  .about-main .chairman-message .desc {
    font-size: 16px;
  }

  .about-main .profile .profile-main {
    padding-top: 60px;
    padding-bottom: 60px;
  }

  .about-main .corporate-vision .vision-main {
    padding-top: 60px;
    padding-bottom: 60px;
  }

  .about-main .enterprise-architecture .architecture-main {
    padding-top: 60px;
    padding-bottom: 40px;
  }

  .about-main .culture .CultureIMgBox .culture-view .culture-item .txt {
    font-size: 18px;
  }

  .about-main .culture .CultureIMgBox .txt {
    padding-top: 10px;
  }

  .about-main .culture .CultureIMgBox .culture-view {
    gap: 25px;
  }
}

@media (max-width: 430px) {
  .about-main .content .title div {
    font-size: 16px;
  }

  .about-main .culture .left {
    width: 330px;
  }

  .about-main .culture .title {
    font-size: 18px;
  }

  .about-main .culture .desc {
    font-size: 18px;
  }
}

@media (max-width: 390px) {
  .about-main .content .title div {
    font-size: 14px;
  }

  .about-main .culture .left {
    width: 300px;
  }

  .about-main .culture .title {
    font-size: 16px;
  }

  .about-main .culture .desc {
    font-size: 16px;
  }

  .about-main .science-park .science-desc {
    font-size: 14px;
  }

  .about-main .science-park {
    height: 300px;
  }
}
</style>
