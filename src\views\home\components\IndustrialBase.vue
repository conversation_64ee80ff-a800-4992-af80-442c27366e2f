<template>
  <div class="industrial-base">
    <div class="industrial-base-main">
      <div class="info-view">
        <div class="left" @click="gotoIndustrialBaseDetails">
          <h3 class="science-title">{{ $t("home.industrialBase") }}</h3>
          <p class="desc">{{ $t("home.base_desc1") }}</p>
          <p class="desc" v-html="$t('home.base_desc2')"></p>
          <div class="more-btn">
            <div class="more-view">
              {{ $t("public.learnMore")
              }}<span class="icon"
                ><el-icon><Right /></el-icon
              ></span>
            </div>
          </div>
        </div>

        <div class="right">
          <el-image class="img" :src="IndustrialBase" fit="cover" lazy />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import IndustrialBase from "@/assets/home/<USER>"

import { useRouter } from "vue-router"

const router = useRouter()

const gotoIndustrialBaseDetails = () => {
  router.push("/business-segment/industrial-base-details")
}
</script>

<style lang="scss" scoped>
.industrial-base {
  .industrial-base-main {
    @extend .responsive-container;

    // 响应式顶部间距
    padding-top: 3rem;

    @include respond-to(md) {
      padding-top: 4rem;
    }

    @include respond-to(lg) {
      padding-top: 5rem;
    }

    @include respond-to(xl) {
      padding-top: 6rem;
    }

    @include respond-to(fhd) {
      padding-top: 7rem;
    }

    @include respond-to(qhd) {
      padding-top: 8rem;
    }

    @include respond-to(uhd) {
      padding-top: 10rem;
    }

    .title {
      font-size: 40px;
      color: rgba(0, 0, 0);
      text-align: center;
    }

    .info-view {
      display: flex;
      align-items: center;

      &:hover {
        .left {
          background: #409eff;

          .science-title,
          .desc,
          .more-btn {
            color: #ffffff;
          }
        }
      }

      .right {
        width: 800px;
        height: 450px;
        position: relative;
        z-index: 20;
        left: -100px;
        box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);

        .img {
          width: 100%;
          height: 100%;
        }
      }

      .left {
        width: 760px;
        padding: 170px 210px 100px 116px;
        position: relative;
        z-index: 10;

        background: #f3f3f3;
        cursor: pointer;

        .science-title {
          font-size: 32px;
          font-weight: bold;
          margin-bottom: 25px;
          text-align: left;
        }
        .desc {
          font-size: 18px;
          color: rgba(0, 0, 0, 0.85);
          text-align: left;
          letter-spacing: 2px;
        }

        .more-btn {
          display: flex;
          align-items: center;
          margin-top: 40px;
          font-size: 16px;
          font-weight: 600;

          .more-view {
            display: flex;
            align-items: center;
          }
        }

        .icon {
          padding-left: 10px;
          font-size: 20px;

          display: flex;
          align-items: center;
        }
      }
    }
  }
}
@import "./media.scss";
</style>
