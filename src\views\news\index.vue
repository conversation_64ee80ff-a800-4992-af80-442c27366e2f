<!--
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-11-05 10:39:46
-->

<template>
  <div class="news-main">
    <div class="background">
      <div class="img">
        <h2 class="title">{{ $t("news.title") }}</h2>
      </div>
    </div>

    <div class="news-content-main">
      <div class="news-content">
        <h2 class="news-title">{{ $t("news.title") }}</h2>
        <div class="news-list">
          <div class="item" v-for="item in newsListData" :key="item.id" @click="goDetails(item.id)">
            <div class="item-content">
              <div class="item-title">{{ getItemTitle(item) }}</div>
              <div class="item-text" v-html="getItemContent(item)"></div>
              <div class="item-time">
                <span>{{ getNewTime(item.newTime) }}</span>
                <span class="icon"
                  ><el-icon><Right /></el-icon
                ></span>
              </div>
            </div>
            <div class="item-img">
              <el-image class="img" :src="item.newBgImg" alt="" fit="cover" />
            </div>
          </div>

          <!-- <div class="pagination">
          <el-pagination
            background
            layout="prev, pager, next"
            :total="50"
            prev-icon="CaretLeft"
            next-icon="CaretRight"
          />
        </div> -->

          <!-- <div class="arrowBox">
          <img src="/resources/images/LeftNormal.png" alt="" />
          <img src="/resources/images/RightNormal.png" alt="" />
        </div> -->
        </div>

        <div class="more-btn" v-if="hasNewsData">
          <div class="btn" @click="getMoreNews">{{ $t("public.loadingMore") }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue"

import { i18n } from "@/i18n"
import { watch } from "vue"

import dayjs from "dayjs"

import { newsList } from "@/api/new/index.js"

import { useRouter } from "vue-router"
const router = useRouter()

const page = ref(1)
const pageSize = ref(9)
const loading = ref(false)
const hasNewsData = ref(true)
const newsListData = ref([])

const lang = ref(i18n.global.locale)
watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  }
)

const getNewsList = () => {
  if (loading.value) return

  loading.value = true

  const params = {
    page: page.value,
    pageSize: pageSize.value
  }
  newsList(params)
    .then((res) => {
      if (res.code == 200) {
        //根据 sort 排序
        res.data.sort((a, b) => a.sort - b.sort)
        newsListData.value = [...newsListData.value, ...res.data]

        if (res.data.length < pageSize.value) {
          hasNewsData.value = false
        }
      }
    })
    .finally(() => {
      loading.value = false
    })
}
getNewsList()

const getMoreNews = () => {
  page.value++
  getNewsList()
}

const getItemTitle = (item) => {
  const selectMap = {
    zh: item.titleZh,
    zh_hk: item.titleHk,
    en: item.titleEn
  }
  return selectMap[lang.value]
}

const getItemContent = (item) => {
  const selectMap = {
    zh: `<p>${item.contentZh.replace(/\n+/g, "\n").replace(/\n/g, "</p><p>")}</p>`,
    zh_hk: `<p>${item.contentHk.replace(/\n+/g, "\n").replace(/\n/g, "</p><p>")}</p>`,
    en: `<p>${item.contentEn.replace(/\n+/g, "\n").replace(/\n/g, "</p><p>")}</p>`
  }
  return selectMap[lang.value]
}

const getNewTime = (time) => {
  return dayjs(time).format("YYYY-MM-DD")
}

const goDetails = (id) => {
  router.push({
    path: "/news/news-details",
    query: {
      id: id
    }
  })
}

onMounted(() => {
  const newsIcons = document.querySelectorAll(".arrowBox img")
  newsIcons.forEach((newsIcon) => {
    newsIcon.addEventListener("mouseover", () => {
      if (newsIcon.src.includes("Left")) {
        newsIcon.src = "/resources/images/LeftHover.png"
      } else {
        newsIcon.src = "/resources/images/RightHover.png"
      }
    })
    newsIcon.addEventListener("mouseout", () => {
      if (newsIcon.src.includes("Left")) {
        newsIcon.src = "/resources/images/LeftNormal.png"
      } else {
        newsIcon.src = "/resources/images/RightNormal.png"
      }
    })
  })
})
</script>

<style lang="scss" scoped>
.news-main {
  background: #f3f3f3;

  .background {
    position: relative;

    .img {
      width: 100%;
      height: 489px;
      background: url("@/assets/news/new-bg.jpg") no-repeat center center;
      background-size: cover;
    }

    .title {
      font-size: 35px;
      color: #000000d9;
      letter-spacing: 5px;

      max-width: 1320px;
      width: 100%;
      margin: 0 auto;

      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateX(-50%);

      text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
    }
  }

  .news-content-main {
    background: #ffffff;
  }

  .news-content {
    width: 100%;
    max-width: 1320px;
    margin: 0 auto;
    min-height: 700px;
    padding-bottom: 100px;

    .news-title {
      font-size: 50px;
      color: rgba(0, 0, 0, 0.85);
      letter-spacing: 5px;
      text-align: center;
      padding-top: 114px;
    }
    .news-list {
      padding-top: 42px;
      display: flex;
      flex-direction: column;
      gap: 40px 20px;
    }

    .more-btn {
      display: flex;
      justify-content: center;
      margin-top: 40px;
      .btn {
        font-size: 20px;
        color: #fff;
        background: #1c2a77;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
      }
    }

    .item {
      gap: 100px;
      padding: 20px;
      border-radius: 5px;
      display: flex;
      transition: all 0.3s;

      &:hover {
        box-shadow: 2px 2px 8px 6px rgba(0, 0, 0, 0.11);
        transform: translateY(-10px);
        cursor: pointer;
      }

      .item-img {
        width: 580px;
        height: 360px;

        .img {
          display: block;
          width: 100%;
          height: 100%;
        }
      }

      .item-content {
        flex: 1;

        padding-top: 50px;

        .item-title {
          height: 80px;

          font-weight: 600;
          font-size: 28px;
          color: rgba(0, 0, 0, 0.85);
          line-height: 40px;
          //letter-spacing: 2px;
          display: -webkit-box;
          overflow: hidden;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
        }

        .item-text {
          font-weight: 300;
          font-size: 18px;
          color: #000000;
          line-height: 24px;
          //letter-spacing: 1px;
          margin-top: 33px;
          display: -webkit-box;
          overflow: hidden;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 4;
          box-sizing: border-box;
        }

        .item-time {
          box-sizing: border-box;
          padding-top: 10px;
          font-weight: 300;
          font-size: 16px;
          color: #000000;
          line-height: 22px;
          letter-spacing: 1px;

          border-top: 1px solid #979797;
          margin-top: 20px;

          display: flex;
          justify-content: space-between;
          align-items: center;
        }
      }
    }

    .pagination {
      margin: 0 auto;
      padding: 80px 0 180px 0;
    }
  }

  .arrowBox {
    display: flex;
    gap: 30px;
    margin-top: 30px;
    cursor: pointer;
    margin-left: 20px;
  }

  .arrowBox img {
    width: 50px;
    height: 50px;
  }
}

  // 响应式样式已经集成在主要样式中，使用新的响应式系统
  // 移除了所有旧的媒体查询

  // 新闻列表响应式优化
  .news-list {
    @extend .responsive-container;
    @extend .responsive-grid;

    .item {
      @extend .responsive-grid-item;

      // 移动端单列流式布局
      flex-direction: column-reverse;

      @include respond-to(md) {
        flex-direction: row;
      }
    }
  }
}
</style>
