/*
 * 响应式设计 Hook
 * 提供响应式状态管理和工具函数
 */

import { ref, onMounted, onUnmounted, computed } from 'vue'
import { 
  BREAKPOINTS, 
  DEVICE_TYPES, 
  getDeviceType, 
  getCurrentBreakpoint,
  isMobile,
  isTablet,
  isDesktop
} from '@/constants/responsive'

export function useResponsive() {
  // 当前窗口宽度
  const windowWidth = ref(window.innerWidth)
  
  // 当前窗口高度
  const windowHeight = ref(window.innerHeight)
  
  // 当前设备类型
  const deviceType = computed(() => getDeviceType(windowWidth.value))
  
  // 当前断点
  const currentBreakpoint = computed(() => getCurrentBreakpoint(windowWidth.value))
  
  // 设备类型判断
  const isMobileDevice = computed(() => isMobile(windowWidth.value))
  const isTabletDevice = computed(() => isTablet(windowWidth.value))
  const isDesktopDevice = computed(() => isDesktop(windowWidth.value))
  
  // 断点判断
  const isXs = computed(() => windowWidth.value < BREAKPOINTS.SM)
  const isSm = computed(() => windowWidth.value >= BREAKPOINTS.SM && windowWidth.value < BREAKPOINTS.MD)
  const isMd = computed(() => windowWidth.value >= BREAKPOINTS.MD && windowWidth.value < BREAKPOINTS.LG)
  const isLg = computed(() => windowWidth.value >= BREAKPOINTS.LG && windowWidth.value < BREAKPOINTS.XL)
  const isXl = computed(() => windowWidth.value >= BREAKPOINTS.XL && windowWidth.value < BREAKPOINTS.XXL)
  const isXxl = computed(() => windowWidth.value >= BREAKPOINTS.XXL)
  
  // 断点范围判断
  const isSmUp = computed(() => windowWidth.value >= BREAKPOINTS.SM)
  const isMdUp = computed(() => windowWidth.value >= BREAKPOINTS.MD)
  const isLgUp = computed(() => windowWidth.value >= BREAKPOINTS.LG)
  const isXlUp = computed(() => windowWidth.value >= BREAKPOINTS.XL)
  const isXxlUp = computed(() => windowWidth.value >= BREAKPOINTS.XXL)
  
  const isSmDown = computed(() => windowWidth.value < BREAKPOINTS.MD)
  const isMdDown = computed(() => windowWidth.value < BREAKPOINTS.LG)
  const isLgDown = computed(() => windowWidth.value < BREAKPOINTS.XL)
  const isXlDown = computed(() => windowWidth.value < BREAKPOINTS.XXL)
  
  // 更新窗口尺寸
  const updateWindowSize = () => {
    windowWidth.value = window.innerWidth
    windowHeight.value = window.innerHeight
  }
  
  // 防抖处理
  let resizeTimer = null
  const handleResize = () => {
    if (resizeTimer) {
      clearTimeout(resizeTimer)
    }
    resizeTimer = setTimeout(updateWindowSize, 100)
  }
  
  // 生命周期管理
  onMounted(() => {
    window.addEventListener('resize', handleResize)
    updateWindowSize()
  })
  
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    if (resizeTimer) {
      clearTimeout(resizeTimer)
    }
  })
  
  // 响应式样式计算
  const getResponsiveValue = (values) => {
    if (typeof values === 'object') {
      if (isXxlUp.value && values.xxl !== undefined) return values.xxl
      if (isXlUp.value && values.xl !== undefined) return values.xl
      if (isLgUp.value && values.lg !== undefined) return values.lg
      if (isMdUp.value && values.md !== undefined) return values.md
      if (isSmUp.value && values.sm !== undefined) return values.sm
      return values.xs || values.default || values
    }
    return values
  }
  
  // 响应式类名生成
  const getResponsiveClasses = (baseClass, breakpointClasses = {}) => {
    const classes = [baseClass]
    
    Object.entries(breakpointClasses).forEach(([breakpoint, className]) => {
      if (className) {
        classes.push(`${breakpoint}:${className}`)
      }
    })
    
    return classes.join(' ')
  }
  
  // 媒体查询匹配
  const matchMedia = (query) => {
    return window.matchMedia(query).matches
  }
  
  // 获取容器最大宽度
  const getContainerMaxWidth = () => {
    if (isXxlUp.value) return '1600px'
    if (isXlUp.value) return '1400px'
    if (isLgUp.value) return '1200px'
    if (isMdUp.value) return '960px'
    if (isSmUp.value) return '720px'
    return '100%'
  }
  
  // 获取响应式字体大小
  const getResponsiveFontSize = (config) => {
    if (isMobileDevice.value) return config.mobile || config.default
    if (isTabletDevice.value) return config.tablet || config.mobile || config.default
    return config.desktop || config.tablet || config.mobile || config.default
  }
  
  // 获取响应式间距
  const getResponsiveSpacing = (config) => {
    if (isMobileDevice.value) return config.mobile || config.default
    if (isTabletDevice.value) return config.tablet || config.mobile || config.default
    return config.desktop || config.tablet || config.mobile || config.default
  }
  
  return {
    // 状态
    windowWidth,
    windowHeight,
    deviceType,
    currentBreakpoint,
    
    // 设备类型
    isMobileDevice,
    isTabletDevice,
    isDesktopDevice,
    
    // 断点判断
    isXs,
    isSm,
    isMd,
    isLg,
    isXl,
    isXxl,
    
    // 断点范围
    isSmUp,
    isMdUp,
    isLgUp,
    isXlUp,
    isXxlUp,
    isSmDown,
    isMdDown,
    isLgDown,
    isXlDown,
    
    // 工具函数
    getResponsiveValue,
    getResponsiveClasses,
    matchMedia,
    getContainerMaxWidth,
    getResponsiveFontSize,
    getResponsiveSpacing,
    
    // 常量
    BREAKPOINTS,
    DEVICE_TYPES
  }
}

// 响应式组合式函数 - 简化版本
export function useBreakpoint() {
  const { currentBreakpoint, isMobileDevice, isTabletDevice, isDesktopDevice } = useResponsive()
  
  return {
    breakpoint: currentBreakpoint,
    isMobile: isMobileDevice,
    isTablet: isTabletDevice,
    isDesktop: isDesktopDevice
  }
}

// 响应式值计算
export function useResponsiveValue(values) {
  const { getResponsiveValue } = useResponsive()
  return computed(() => getResponsiveValue(values))
}
