// 全局 CSS 变量
@import "./variables.css";
// Transition
@import "./transition.scss";
// Element Plus
@import "./element-plus.css";
@import "./element-plus.scss";

// 注册多主题
@import "./theme/register.scss";
// Mixins
@import "./mixins.scss";
// 响应式工具类
@import "./responsive-utilities.scss";
// View Transition
@import "./view-transition.scss";

// Theme 自定义的
@import "./mixins-theme-normal.scss";
@import "./mixins-theme-dark.scss";
@import "./mixins-theme-dark-blue.scss";

// 业务页面几乎都应该在根元素上挂载 class="app-container"，以保持页面美观
.app-container {
  padding: 1rem;

  @include respond-to(md) {
    padding: 1.5rem;
  }

  @include respond-to(lg) {
    padding: 2rem;
  }

  @include respond-to(xl) {
    padding: 2.5rem;
  }

  @include respond-to(xxl) {
    padding: 3rem;
  }

  @include respond-to(fhd) {
    padding: 3.5rem;
  }

  @include respond-to(qhd) {
    padding: 4rem;
  }

  @include respond-to(uhd) {
    padding: 5rem;
  }
}

html {
  height: 100%;
}

body {
  height: 100%;
  color: var(--v3-body-text-color);
  background-color: var(--v3-body-bg-color);
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial,
    sans-serif;
  font-display: block;
  @extend %scrollbar;
}

#app {
  height: 100%;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

a,
a:focus,
a:hover {
  color: inherit;
  outline: none;
  text-decoration: none;
}

div:focus {
  outline: none;
}

// 修改进度条的样式
#nprogress {
  .bar {
    background: #efc8a5;
  }
}

// 主题中需要自定义的样式
html.normal {
  @extend %theme-normal-custom;
}
html.dark {
  @extend %theme-dark-custom;
}
html.dark-blue {
  @extend %theme-dark-blue-custom;
}

// 全局响应式容器样式
.responsive-container {
  width: 100%;
  margin: 0 auto;
  padding: 0 1rem;

  @include respond-to(md) {
    padding: 0 1.5rem;
    max-width: 800px;
  }

  @include respond-to(lg) {
    padding: 0 2rem;
    max-width: 1200px;
  }

  @include respond-to(xl) {
    padding: 0 2.5rem;
    max-width: 1400px;
  }

  @include respond-to(xxl) {
    padding: 0 3rem;
    max-width: 1600px;
  }

  @include respond-to(fhd) {
    padding: 0 4rem;
    max-width: 1800px;
  }

  @include respond-to(qhd) {
    padding: 0 5rem;
    max-width: 2200px;
  }

  @include respond-to(uhd) {
    padding: 0 6rem;
    max-width: 3200px;
  }
}

// 全局响应式网格容器
.responsive-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  @include respond-to(md) {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 2rem;
  }

  @include respond-to(lg) {
    gap: 2.5rem;
  }

  @include respond-to(xl) {
    gap: 3rem;
  }

  @include respond-to(xxl) {
    gap: 3.5rem;
  }

  @include respond-to(fhd) {
    gap: 4rem;
  }

  @include respond-to(qhd) {
    gap: 4.5rem;
  }

  @include respond-to(uhd) {
    gap: 5rem;
  }

  // 网格项目
  &-item {
    flex: 1 1 100%;

    &.col-2 {
      @include respond-to(md) {
        flex: 1 1 calc(50% - 1rem);
      }
    }

    &.col-3 {
      @include respond-to(md) {
        flex: 1 1 calc(50% - 1rem);
      }

      @include respond-to(lg) {
        flex: 1 1 calc(33.333% - 1.667rem);
      }
    }

    &.col-4 {
      @include respond-to(md) {
        flex: 1 1 calc(50% - 1rem);
      }

      @include respond-to(lg) {
        flex: 1 1 calc(33.333% - 1.667rem);
      }

      @include respond-to(xl) {
        flex: 1 1 calc(25% - 2.25rem);
      }
    }
  }
}
