<!-- eslint-disable no-unused-vars -->
<!-- eslint-disable no-unused-vars -->
<!-- eslint-disable no-unused-vars -->
<!--
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-09-25 15:20:15
-->

<template>
  <div class="business-details-main">
    <div class="sub-title">
      <ResponsiveContainer type="container" class="top-breadcrumb">
        <el-breadcrumb :separator-icon="ArrowRight">
          <el-breadcrumb-item :to="{ path: '/' }">{{ $t("menus.home") }}</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/business-segment' }">{{ $t("businessSegment.title") }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ businessSegmentInfo[lang].title }}</el-breadcrumb-item>
        </el-breadcrumb>
      </ResponsiveContainer>
    </div>

    <div class="content-main">
      <div class="content">
        <!-- 科研中心 -->
        <ResponsiveContainer type="container" class="info-container">
          <div class="info">
            <div class="title">{{ businessSegmentInfo[lang].title }}</div>
            <div class="desc" v-html="businessSegmentInfo[lang].homeDesc"></div>
          </div>
        </ResponsiveContainer>

        <ResponsiveContainer type="container" class="img-container">
          <div class="img-content">
            <div class="img-view">
              <el-image class="img" :src="businessSegmentInfo.img" fit="cover" lazy />
            </div>
            <div class="sub-tag">
              <div class="sub-tag-item">
                <div class="sub-tag1">{{ businessSegmentInfo[lang].center1 }}</div>
                <div class="sub-tag2">
                  <el-image class="imgpos" :src="businessSegmentInfo.position_img" lazy />
                  {{ businessSegmentInfo[lang].center1_pos }}
                </div>
              </div>
              <div class="sub-tag-item">
                <div class="sub-tag1">{{ businessSegmentInfo[lang].center2 }}</div>
                <div class="sub-tag2">
                  <el-image class="imgpos" :src="businessSegmentInfo.position_img" lazy />
                  {{ businessSegmentInfo[lang].center2_pos }}
                </div>
              </div>
            </div>
          </div>
        </ResponsiveContainer>

      </div>
      <!-- 科研板块 -->
      <ResponsiveContainer type="section" class="research-institute">
        <div class="research-header">
          <h2 class="research-title">{{ academySciencesData[lang].title }}</h2>
          <p class="research-desc">{{ academySciencesData[lang].desc }}</p>
        </div>
        <div class="research-bt-div">
          <el-button
            v-for="item in academySciencesData[lang].children"
            :key="item.id"
            :class="{'btn-research': item.id !== activeIndex, 'btn-active': item.id == activeIndex}"
            round="true"
            @click="btnClick(item)"
          >
            {{ item.title }}
          </el-button>
        </div>
        <div class="research-institute-imgs" v-if="activeIndex != 2">
          <div
            class="carouselItemView"
            v-for="(item, index) in academySciencesData[lang].children[activeIndex].imgs"
            :key="index"
            @click="imgClick(item)"
          >
            <el-image :src="item.imgurl" fit="cover" class="research-image" lazy />
            <div class="text-overlay">
              {{ item.desc }}
              <div class="overlay-action">
                <span>{{ item.more }}</span>
                <el-icon><Right /></el-icon>
              </div>
            </div>
          </div>
        </div>
        <div class="research-carousel-wrapper" v-if="activeIndex == 2">
          <el-carousel
            class="research-carousel"
            height="300px"
            indicator-position="none"
            arrow="always"
            :autoplay="false"
            type="card"
            loop="true"
            initial-index="1"
            motion-blur="true"
            cardScale="1"
            ref="carouselRef"
          >
            <el-carousel-item
              class="research-item"
              v-for="(item, index) in academySciencesData[lang].children[activeIndex].imgs"
              :key="index"
            >
              <div class="carouselItemView" @click="carouselImgClick(item)">
                <el-image class="carousel-image" :src="item.imgurl" fit="cover" lazy />
                <div class="text-overlay">
                  {{ item.desc }}
                  <div class="overlay-action">
                    <span>{{ item.more }}</span>
                    <el-icon><Right /></el-icon>
                  </div>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
      </ResponsiveContainer>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, ref } from "vue"
import { ArrowRight, Right } from "@element-plus/icons-vue"
import { onBeforeMount, reactive } from "vue"

import BusinessSegmentList from "./business-segment-list"
import AcademySciences_Section from "./academy-sciences-section"
import ResponsiveContainer from "@/components/ResponsiveContainer/index.vue"

import { i18n } from "@/i18n"
import { watch } from "vue"

import { useRouter } from "vue-router"

const carouselRef = ref(null);
const router = useRouter()
const activeIndex = ref(0)
const btnClick = (item) => {
  nextTick(() => {
    activeIndex.value = item.id;    
    console.log(item.id,item.imgs);

  })
}

const academySciencesData = reactive(AcademySciences_Section)
const lang = ref(i18n.global.locale)

let businessSegmentInfo = reactive({
  key: "",
  title: "",
  desc: "",
  img: "",
  position_img: ""
})

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  }
)

const updateBusinessSegmentInfo = (key) => {
  const foundSegment = BusinessSegmentList.find((item) => item.key === key)
  if (foundSegment) {
    Object.assign(businessSegmentInfo, foundSegment)
  }
}

const gotoScienceAcademyDetails = (item) => {
  router.push({
    path: "/business-segment/science-academy-details",
    query: {
      key: item.key
    }
  })
}

const imgClick = (item) =>{
  
  if(item.url.trim() == ''){
    event?.stopPropagation()
    return;
  }
  router.push({
    path: item.url,
    query: {
      // key: item.key
    }
  })
}
const carouselImgClick = (item) =>{
  
  if(item.url.trim() == ''){
    return;
  }
  router.push({
    path: item.url,
    query: {
      // key: item.key
    }
  })
}
onBeforeMount(() => {
  updateBusinessSegmentInfo("AcademyOfSciences")
})

</script>

<style lang="scss" scoped>
@import "@/styles/responsive-utilities.scss";

.business-details-main {
  background: #f2f3f5;
  font-family: SourceHanSansCN, SourceHanSansCN;
  width: 100%;
  min-height: 100vh;

  .sub-title {
    @include responsive-spacing(height, 80px, 90px, 100px);
    background: #f3f3f3;
    display: flex;
    align-items: center;

    .top-breadcrumb {
      @include responsive-spacing(padding-top, 0, 0, 0);
      color: rgba(0, 0, 0, 0.85);
      line-height: 25px;

      :deep(.el-breadcrumb) {
        @include responsive-font-size(16px, 17px, 18px);

        .el-breadcrumb__inner a:hover,
        .el-breadcrumb__inner.is-link:hover {
          color: inherit;
        }
      }
    }
  }

  .content-main {
    background: #f2f3f5;
  }

  .content {
    background-image: url(@/assets/business-segment/segment1bg.png);
    background-size: cover;
    background-position: center;
    @include responsive-spacing(padding, 2rem 0, 3rem 0, 4rem 0);

    .info-container {
      text-align: center;

      // 在超大屏幕上限制最大宽度并居中
      @include respond-to(xxl) {
        max-width: 1200px;
        margin-left: auto;
        margin-right: auto;
      }

      .info {
        .title {
          @include responsive-font-size(24px, 28px, 32px);
          font-weight: 600;
          color: rgba(0, 0, 0, 0.85);
          line-height: 1.4;
          letter-spacing: 1px;
          margin-bottom: 1rem;

          @include respond-to(md) {
            letter-spacing: 2px;
          }
        }

        .desc {
          @include responsive-font-size(16px, 18px, 20px);
          color: #000000;
          line-height: 1.6;
          letter-spacing: 1px;

          @include respond-to(md) {
            letter-spacing: 2px;
          }
        }
      }
    }
    .img-container {
      @include responsive-spacing(margin-top, 2rem, 3rem, 4rem);

      // 在超大屏幕上限制最大宽度并居中
      @include respond-to(xxl) {
        max-width: 1400px;
        margin-left: auto;
        margin-right: auto;
      }

      .img-content {
        @include responsive-flex(column, row);
        align-items: center;
        justify-content: center;
        gap: 1.5rem;
        max-width: 100%;
        margin: 0 auto;
        padding: 0 1rem;

        // 平板横屏/小笔记本
        @include respond-to(lg) {
          gap: 3rem;
          align-items: flex-start;
          justify-content: space-between;
          padding: 0 2rem;
          max-width: 1200px;
        }

        // 标准笔记本
        @include respond-to(xl) {
          gap: 4rem;
          padding: 0 2.5rem;
          max-width: 1400px;
        }

        // 标准桌面 (1440p)
        @include respond-to(xxl) {
          gap: 5rem;
          padding: 0 3rem;
          max-width: 1600px;
        }

        // 全高清桌面 (1080p) - 主要基准
        @include respond-to(fhd) {
          gap: 6rem;
          padding: 0 4rem;
          max-width: 1800px;
        }

        // 2K显示器优化
        @include respond-to(qhd) {
          gap: 7rem;
          padding: 0 5rem;
          max-width: 2200px;
        }

        // 4K显示器优化
        @include respond-to(uhd) {
          gap: 8rem;
          padding: 0 6rem;
          max-width: 3200px;
        }
      }

      .img-view {
        flex: 1;
        max-width: 100%;

        // 平板横屏/小笔记本
        @include respond-to(lg) {
          max-width: 55%;
        }

        // 标准笔记本
        @include respond-to(xl) {
          max-width: 52%;
        }

        // 标准桌面 (1440p)
        @include respond-to(xxl) {
          max-width: 50%;
        }

        // 全高清桌面 (1080p) - 主要基准
        @include respond-to(fhd) {
          max-width: 48%;
        }

        .img {
          display: block;
          width: 100%;
          height: auto;
          max-height: 300px;
          object-fit: cover;
          border-radius: 8px;
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
          transition: transform 0.3s ease, box-shadow 0.3s ease;

          // 平板横屏/小笔记本
          @include respond-to(lg) {
            max-height: 350px;
            border-radius: 12px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
          }

          // 标准笔记本
          @include respond-to(xl) {
            max-height: 400px;
            border-radius: 14px;
          }

          // 标准桌面 (1440p)
          @include respond-to(xxl) {
            max-height: 450px;
            border-radius: 16px;
          }

          // 全高清桌面 (1080p) - 主要基准
          @include respond-to(fhd) {
            max-height: 500px;
            border-radius: 18px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
          }

          // 2K显示器优化
          @include respond-to(qhd) {
            max-height: 600px;
            border-radius: 20px;
            box-shadow: 0 10px 28px rgba(0, 0, 0, 0.14);
          }

          // 4K显示器优化
          @include respond-to(uhd) {
            max-height: 800px;
            border-radius: 24px;
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.16);
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);

            @include respond-to(fhd) {
              transform: translateY(-3px);
              box-shadow: 0 16px 40px rgba(0, 0, 0, 0.18);
            }
          }
        }
      }

      .sub-tag {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 1.5rem;

        // 平板横屏/小笔记本
        @include respond-to(lg) {
          max-width: 42%;
          gap: 2rem;
        }

        // 标准笔记本
        @include respond-to(xl) {
          max-width: 45%;
          gap: 2.5rem;
        }

        // 标准桌面 (1440p)
        @include respond-to(xxl) {
          max-width: 47%;
          gap: 3rem;
        }

        // 全高清桌面 (1080p) - 主要基准
        @include respond-to(fhd) {
          max-width: 50%;
          gap: 3.5rem;
        }

        .sub-tag-item {
          padding: 1.5rem;
          background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
          border-radius: 12px;
          border: 1px solid #e8eaff;
          transition: all 0.3s ease;

          // 平板横屏/小笔记本
          @include respond-to(lg) {
            padding: 1.75rem;
            border-radius: 14px;
          }

          // 标准笔记本
          @include respond-to(xl) {
            padding: 2rem;
            border-radius: 16px;
          }

          // 标准桌面 (1440p)
          @include respond-to(xxl) {
            padding: 2.25rem;
            border-radius: 18px;
          }

          // 全高清桌面 (1080p) - 主要基准
          @include respond-to(fhd) {
            padding: 2.5rem;
            border-radius: 20px;
          }

          // 2K显示器优化
          @include respond-to(qhd) {
            padding: 3rem;
            border-radius: 22px;
          }

          // 4K显示器优化
          @include respond-to(uhd) {
            padding: 4rem;
            border-radius: 24px;
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(31, 42, 114, 0.1);
            border-color: #1f2a72;

            @include respond-to(fhd) {
              transform: translateY(-3px);
              box-shadow: 0 12px 32px rgba(31, 42, 114, 0.12);
            }
          }

          .sub-tag1 {
            color: #1f2a72;
            font-weight: 700;
            line-height: 1.3;
            margin-bottom: 0.75rem;
            font-size: 18px;

            @include respond-to(lg) {
              font-size: 20px;
              margin-bottom: 0.875rem;
            }

            @include respond-to(xl) {
              font-size: 22px;
              margin-bottom: 1rem;
            }

            @include respond-to(xxl) {
              font-size: 24px;
              margin-bottom: 1.125rem;
            }

            @include respond-to(fhd) {
              font-size: 26px;
              margin-bottom: 1.25rem;
            }

            @include respond-to(qhd) {
              font-size: 28px;
              margin-bottom: 1.375rem;
            }

            @include respond-to(uhd) {
              font-size: 32px;
              margin-bottom: 1.5rem;
            }
          }

          .sub-tag2 {
            font-weight: 500;
            line-height: 1.6;
            color: #555;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 14px;

            @include respond-to(lg) {
              font-size: 15px;
              gap: 0.875rem;
            }

            @include respond-to(xl) {
              font-size: 16px;
              gap: 1rem;
            }

            @include respond-to(xxl) {
              font-size: 17px;
              gap: 1.125rem;
            }

            @include respond-to(fhd) {
              font-size: 18px;
              gap: 1.25rem;
            }

            @include respond-to(qhd) {
              font-size: 20px;
              gap: 1.375rem;
            }

            @include respond-to(uhd) {
              font-size: 24px;
              gap: 1.5rem;
            }

            .imgpos {
              flex-shrink: 0;
              width: 16px;
              height: 16px;

              @include respond-to(lg) {
                width: 18px;
                height: 18px;
              }

              @include respond-to(xl) {
                width: 20px;
                height: 20px;
              }

              @include respond-to(xxl) {
                width: 22px;
                height: 22px;
              }

              @include respond-to(fhd) {
                width: 24px;
                height: 24px;
              }

              @include respond-to(qhd) {
                width: 26px;
                height: 26px;
              }

              @include respond-to(uhd) {
                width: 32px;
                height: 32px;
              }
            }
          }
        }
      }
    }
  }

  .research-institute {
    background: #ffffff;

    // 在超大屏幕上限制最大宽度并居中
    @include respond-to(xxl) {
      max-width: 1400px;
      margin-left: auto;
      margin-right: auto;
    }

    .research-header {
      text-align: center;
      margin-bottom: 3rem;

      .research-title {
        @include responsive-font-size(28px, 32px, 36px);
        font-weight: 600;
        color: #333;
        line-height: 1.4;
        margin: 0 0 1rem 0;
      }

      .research-desc {
        @include responsive-font-size(16px, 18px, 20px);
        color: #666;
        line-height: 1.6;
        margin: 0 auto;
        max-width: 800px;

        @include respond-to(md) {
          max-width: 600px;
        }
      }
    }

    .research-bt-div {
      text-align: center;
      margin-bottom: 3rem;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 0.5rem;

      @include respond-to(md) {
        gap: 1rem;
      }
    }

    .btn-research {
      background-color: #f2f3f5;
      color: #333;
      border: 2px solid transparent;
      @include responsive-font-size(16px, 18px, 20px);
      @include responsive-spacing(padding, 0.5rem 1rem, 0.6rem 1.2rem, 0.7rem 1.5rem);
      transition: all 0.3s ease;

      &:hover {
        background-color: #078CEC;
        color: white;
        border-color: #078CEC;
        transform: translateY(-2px);
      }
    }

    .btn-active {
      background-color: #078CEC;
      color: white;
      border: 2px solid #078CEC;
      @include responsive-font-size(16px, 18px, 20px);
      @include responsive-spacing(padding, 0.5rem 1rem, 0.6rem 1.2rem, 0.7rem 1.5rem);
    }

    /* 轮播图样式 */
    :deep(.el-carousel__item:not(.is-active)) {
      opacity: 0.6;
      transition: opacity 0.3s ease;
    }

    :deep(.el-carousel__item) {
      @include responsive-spacing(padding, 0 4px, 0 6px, 0 8px);
    }
  }
  // 普通网格布局样式 - 移动端单列流式布局
  .research-institute-imgs {
    width: 100%;
    display: flex;
    flex-direction: column; // 移动端单列流式布局
    gap: 1.5rem;
    margin: 0 auto;
    padding: 0 1rem;

    // 平板开始使用多列布局
    @include respond-to(md) {
      flex-direction: row;
      flex-wrap: wrap;
      gap: 2rem;
      max-width: 800px;
      padding: 0 1.5rem;
    }

    // 标准笔记本
    @include respond-to(xl) {
      gap: 2.5rem;
      max-width: 1200px;
      padding: 0 2rem;
    }

    // 标准桌面 (1440p)
    @include respond-to(xxl) {
      gap: 3rem;
      max-width: 1400px;
      padding: 0 2.5rem;
    }

    // 全高清桌面 (1080p) - 主要基准
    @include respond-to(fhd) {
      gap: 3.5rem;
      max-width: 1600px;
      padding: 0 3rem;
    }

    // 2K显示器优化
    @include respond-to(qhd) {
      gap: 4rem;
      max-width: 2000px;
      padding: 0 4rem;
    }

    // 4K显示器优化
    @include respond-to(uhd) {
      gap: 5rem;
      max-width: 2800px;
      padding: 0 6rem;
    }

    // 响应式列数
    .carouselItemView {
      flex: 1 1 100%; // 移动端：单列流式布局

      @include respond-to(md) {
        flex: 1 1 calc(50% - 1rem); // 平板：2列
      }

      @include respond-to(lg) {
        flex: 1 1 calc(33.333% - 1.333rem); // 桌面：3列
      }

      // 在超大屏幕上保持3列，但增加最大宽度限制
      @include respond-to(fhd) {
        flex: 1 1 calc(33.333% - 2.333rem);
        max-width: 500px; // 防止卡片过大
      }

      @include respond-to(qhd) {
        max-width: 600px;
      }

      @include respond-to(uhd) {
        max-width: 800px;
      }
    }
  }

  // 轮播图专用容器 - 简洁样式
  .research-carousel-wrapper {
    width: 100%;
    margin: 0 auto;
    padding: 0;
  }

  .carouselItemView {
    position: relative;
    width: 100%;
    max-width: 100%;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

      .text-overlay {
        background: rgba(0, 0, 0, 0.8);
      }
    }

    .research-image,
    .carousel-image {
      width: 100%;
      @include responsive-spacing(height, 250px, 280px, 300px);
      object-fit: cover;
    }
  }

  .text-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    @include responsive-spacing(padding, 1.5rem 1rem 1rem, 2rem 1.5rem 1.5rem, 2.5rem 2rem 2rem);
    transition: background 0.3s ease;

    @include responsive-font-size(16px, 18px, 20px);
    line-height: 1.4;

    .overlay-action {
      @include responsive-spacing(margin-top, 0.8rem, 1rem, 1.2rem);
      @include responsive-font-size(14px, 15px, 16px);
      display: flex;
      align-items: center;
      gap: 0.5rem;
      opacity: 0.9;

      .el-icon {
        transition: transform 0.3s ease;
      }
    }

    &:hover .overlay-action .el-icon {
      transform: translateX(4px);
    }
  }

  // 轮播图样式 - 简洁清晰
  .research-carousel {
    width: 100%;
    margin: 0 auto;

    .carouselItemView {
      margin: 0 auto;
      max-width: 600px;

      @include respond-to(lg) {
        max-width: 700px;
      }

      @include respond-to(xl) {
        max-width: 800px;
      }
    }
  }
}
</style>
