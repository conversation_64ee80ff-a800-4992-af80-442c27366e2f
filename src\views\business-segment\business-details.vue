<!-- eslint-disable no-unused-vars -->
<!-- eslint-disable no-unused-vars -->
<!-- eslint-disable no-unused-vars -->
<!--
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-09-25 15:20:15
-->

<template>
  <div class="business-details-main">
    <div class="sub-title">
      <ResponsiveContainer type="container" class="top-breadcrumb">
        <el-breadcrumb :separator-icon="ArrowRight">
          <el-breadcrumb-item :to="{ path: '/' }">{{ $t("menus.home") }}</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/business-segment' }">{{ $t("businessSegment.title") }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ businessSegmentInfo[lang].title }}</el-breadcrumb-item>
        </el-breadcrumb>
      </ResponsiveContainer>
    </div>

    <div class="content-main">
      <div class="content">
        <!-- 科研中心 -->
        <ResponsiveContainer type="container" class="info-container">
          <div class="info">
            <div class="title">{{ businessSegmentInfo[lang].title }}</div>
            <div class="desc" v-html="businessSegmentInfo[lang].homeDesc"></div>
          </div>
        </ResponsiveContainer>

        <ResponsiveContainer type="container" class="img-container">
          <div class="img-content">
            <div class="img-view">
              <el-image class="img" :src="businessSegmentInfo.img" fit="cover" lazy />
            </div>
            <div class="sub-tag">
              <div class="sub-tag-item">
                <div class="sub-tag1">{{ businessSegmentInfo[lang].center1 }}</div>
                <div class="sub-tag2">
                  <el-image class="imgpos" :src="businessSegmentInfo.position_img" lazy />
                  {{ businessSegmentInfo[lang].center1_pos }}
                </div>
              </div>
              <div class="sub-tag-item">
                <div class="sub-tag1">{{ businessSegmentInfo[lang].center2 }}</div>
                <div class="sub-tag2">
                  <el-image class="imgpos" :src="businessSegmentInfo.position_img" lazy />
                  {{ businessSegmentInfo[lang].center2_pos }}
                </div>
              </div>
            </div>
          </div>
        </ResponsiveContainer>

      </div>
      <!-- 科研板块 -->
      <ResponsiveContainer type="section" class="research-institute">
        <div class="research-header">
          <h2 class="research-title">{{ academySciencesData[lang].title }}</h2>
          <p class="research-desc">{{ academySciencesData[lang].desc }}</p>
        </div>
        <div class="research-bt-div">
          <el-button
            v-for="item in academySciencesData[lang].children"
            :key="item.id"
            :class="{'btn-research': item.id !== activeIndex, 'btn-active': item.id == activeIndex}"
            round="true"
            @click="btnClick(item)"
          >
            {{ item.title }}
          </el-button>
        </div>
        <div class="research-institute-imgs" v-if="activeIndex != 2">
          <div
            class="carouselItemView"
            v-for="(item, index) in academySciencesData[lang].children[activeIndex].imgs"
            :key="index"
            @click="imgClick(item)"
          >
            <el-image :src="item.imgurl" fit="cover" class="research-image" lazy />
            <div class="text-overlay">
              {{ item.desc }}
              <div class="overlay-action">
                <span>{{ item.more }}</span>
                <el-icon><Right /></el-icon>
              </div>
            </div>
          </div>
        </div>
        <div class="research-institute-imgs carousel-container" v-if="activeIndex == 2">
          <el-carousel
            class="research-carousel"
            height="300px"
            indicator-position="none"
            arrow="always"
            :autoplay="false"
            type="card"
            loop="true"
            initial-index="1"
            motion-blur="true"
            cardScale="1"
            ref="carouselRef"
          >
            <el-carousel-item
              class="research-item"
              v-for="(item, index) in academySciencesData[lang].children[activeIndex].imgs"
              :key="index"
            >
              <div class="carouselItemView" @click="carouselImgClick(item)">
                <el-image class="carousel-image" :src="item.imgurl" fit="cover" lazy />
                <div class="text-overlay">
                  {{ item.desc }}
                  <div class="overlay-action">
                    <span>{{ item.more }}</span>
                    <el-icon><Right /></el-icon>
                  </div>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
      </ResponsiveContainer>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, ref } from "vue"
import { ArrowRight, Right } from "@element-plus/icons-vue"
import { onBeforeMount, reactive } from "vue"

import BusinessSegmentList from "./business-segment-list"
import AcademySciences_Section from "./academy-sciences-section"
import ResponsiveContainer from "@/components/ResponsiveContainer/index.vue"

import { i18n } from "@/i18n"
import { watch } from "vue"

import { useRouter } from "vue-router"

const carouselRef = ref(null);
const router = useRouter()
const activeIndex = ref(0)
const btnClick = (item) => {
  nextTick(() => {
    activeIndex.value = item.id;    
    console.log(item.id,item.imgs);

  })
}

const academySciencesData = reactive(AcademySciences_Section)
const lang = ref(i18n.global.locale)

let businessSegmentInfo = reactive({
  key: "",
  title: "",
  desc: "",
  img: "",
  position_img: ""
})

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  }
)

const updateBusinessSegmentInfo = (key) => {
  const foundSegment = BusinessSegmentList.find((item) => item.key === key)
  if (foundSegment) {
    Object.assign(businessSegmentInfo, foundSegment)
  }
}

const gotoScienceAcademyDetails = (item) => {
  router.push({
    path: "/business-segment/science-academy-details",
    query: {
      key: item.key
    }
  })
}

const imgClick = (item) =>{
  
  if(item.url.trim() == ''){
    event?.stopPropagation()
    return;
  }
  router.push({
    path: item.url,
    query: {
      // key: item.key
    }
  })
}
const carouselImgClick = (item) =>{
  
  if(item.url.trim() == ''){
    return;
  }
  router.push({
    path: item.url,
    query: {
      // key: item.key
    }
  })
}
onBeforeMount(() => {
  updateBusinessSegmentInfo("AcademyOfSciences")
})

</script>

<style lang="scss" scoped>
@import "@/styles/responsive-utilities.scss";

.business-details-main {
  background: #f2f3f5;
  font-family: SourceHanSansCN, SourceHanSansCN;
  width: 100%;
  min-height: 100vh;

  .sub-title {
    @include responsive-spacing(height, 80px, 90px, 100px);
    background: #f3f3f3;
    display: flex;
    align-items: center;

    .top-breadcrumb {
      @include responsive-spacing(padding-top, 0, 0, 0);
      color: rgba(0, 0, 0, 0.85);
      line-height: 25px;

      :deep(.el-breadcrumb) {
        @include responsive-font-size(16px, 17px, 18px);

        .el-breadcrumb__inner a:hover,
        .el-breadcrumb__inner.is-link:hover {
          color: inherit;
        }
      }
    }
  }

  .content-main {
    background: #f2f3f5;
  }

  .content {
    background-image: url(@/assets/business-segment/segment1bg.png);
    background-size: cover;
    background-position: center;
    @include responsive-spacing(padding, 2rem 0, 3rem 0, 4rem 0);

    .info-container {
      text-align: center;

      // 在超大屏幕上限制最大宽度并居中
      @include respond-to(xxl) {
        max-width: 1200px;
        margin-left: auto;
        margin-right: auto;
      }

      .info {
        .title {
          @include responsive-font-size(24px, 28px, 32px);
          font-weight: 600;
          color: rgba(0, 0, 0, 0.85);
          line-height: 1.4;
          letter-spacing: 1px;
          margin-bottom: 1rem;

          @include respond-to(md) {
            letter-spacing: 2px;
          }
        }

        .desc {
          @include responsive-font-size(16px, 18px, 20px);
          color: #000000;
          line-height: 1.6;
          letter-spacing: 1px;

          @include respond-to(md) {
            letter-spacing: 2px;
          }
        }
      }
    }
    .img-container {
      @include responsive-spacing(margin-top, 2rem, 3rem, 4rem);

      // 在超大屏幕上限制最大宽度并居中
      @include respond-to(xxl) {
        max-width: 1400px;
        margin-left: auto;
        margin-right: auto;
      }

      .img-content {
        @include responsive-flex(column, row);
        align-items: center;
        justify-content: center;
        gap: 2rem;

        @include respond-to(lg) {
          gap: 3rem;
          align-items: flex-start;
          justify-content: space-between;
        }
      }

      .img-view {
        flex: 1;
        max-width: 100%;

        @include respond-to(lg) {
          max-width: 60%;
        }

        .img {
          display: block;
          width: 100%;
          height: auto;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
      }

      .sub-tag {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 1.5rem;

        @include respond-to(lg) {
          max-width: 35%;
          gap: 2rem;
        }

        .sub-tag-item {
          .sub-tag1 {
            @include responsive-font-size(20px, 22px, 24px);
            color: #1f2a72;
            font-weight: 600;
            line-height: 1.4;
            margin-bottom: 0.5rem;
          }

          .sub-tag2 {
            @include responsive-font-size(16px, 17px, 18px);
            font-weight: 500;
            line-height: 1.6;
            color: #333;
            display: flex;
            align-items: center;
            gap: 0.5rem;

            .imgpos {
              width: 16px;
              height: 16px;
              flex-shrink: 0;
            }
          }
        }
      }
    }
  }

  .research-institute {
    background: #ffffff;

    // 在超大屏幕上限制最大宽度并居中
    @include respond-to(xxl) {
      max-width: 1400px;
      margin-left: auto;
      margin-right: auto;
    }

    .research-header {
      text-align: center;
      margin-bottom: 3rem;

      .research-title {
        @include responsive-font-size(28px, 32px, 36px);
        font-weight: 600;
        color: #333;
        line-height: 1.4;
        margin: 0 0 1rem 0;
      }

      .research-desc {
        @include responsive-font-size(16px, 18px, 20px);
        color: #666;
        line-height: 1.6;
        margin: 0 auto;
        max-width: 800px;

        @include respond-to(md) {
          max-width: 600px;
        }
      }
    }

    .research-bt-div {
      text-align: center;
      margin-bottom: 3rem;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 0.5rem;

      @include respond-to(md) {
        gap: 1rem;
      }
    }

    .btn-research {
      background-color: #f2f3f5;
      color: #333;
      border: 2px solid transparent;
      @include responsive-font-size(16px, 18px, 20px);
      @include responsive-spacing(padding, 0.5rem 1rem, 0.6rem 1.2rem, 0.7rem 1.5rem);
      transition: all 0.3s ease;

      &:hover {
        background-color: #078CEC;
        color: white;
        border-color: #078CEC;
        transform: translateY(-2px);
      }
    }

    .btn-active {
      background-color: #078CEC;
      color: white;
      border: 2px solid #078CEC;
      @include responsive-font-size(16px, 18px, 20px);
      @include responsive-spacing(padding, 0.5rem 1rem, 0.6rem 1.2rem, 0.7rem 1.5rem);
    }

    /* 轮播图样式 */
    :deep(.el-carousel__item:not(.is-active)) {
      opacity: 0.6;
      transition: opacity 0.3s ease;
    }

    :deep(.el-carousel__item) {
      @include responsive-spacing(padding, 0 4px, 0 6px, 0 8px);
    }
  }
  .research-institute-imgs {
    width: 100%;
    @include responsive-grid(1, 2, 3);
    gap: 1.5rem;

    @include respond-to(md) {
      gap: 2rem;
    }

    // 普通网格布局在大屏幕上限制宽度
    &:not(.carousel-container) {
      max-width: 1200px;
      margin: 0 auto;

      @include respond-to(xl) {
        max-width: 1000px;
      }
    }

    // 轮播图容器专用样式 - 取消grid布局
    &.carousel-container {
      display: block !important;
      grid-template-columns: none !important;
      grid-template-rows: none !important;
      gap: 0 !important;
      max-width: none !important;
      margin: 0 !important;
    }
  }

  .carouselItemView {
    position: relative;
    width: 100%;
    max-width: 100%;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

      .text-overlay {
        background: rgba(0, 0, 0, 0.8);
      }
    }

    .research-image,
    .carousel-image {
      width: 100%;
      @include responsive-spacing(height, 250px, 280px, 300px);
      object-fit: cover;
    }
  }

  .text-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    @include responsive-spacing(padding, 1.5rem 1rem 1rem, 2rem 1.5rem 1.5rem, 2.5rem 2rem 2rem);
    transition: background 0.3s ease;

    @include responsive-font-size(16px, 18px, 20px);
    line-height: 1.4;

    .overlay-action {
      @include responsive-spacing(margin-top, 0.8rem, 1rem, 1.2rem);
      @include responsive-font-size(14px, 15px, 16px);
      display: flex;
      align-items: center;
      gap: 0.5rem;
      opacity: 0.9;

      .el-icon {
        transition: transform 0.3s ease;
      }
    }

    &:hover .overlay-action .el-icon {
      transform: translateX(4px);
    }
  }

  // 轮播图专用样式 - 完全重置布局
  .carousel-container {
    // 完全重置所有grid相关属性
    display: block !important;
    grid: none !important;
    grid-template: none !important;
    grid-area: none !important;
    width: 100% !important;

    .research-carousel {
      width: 100% !important;
      margin: 0 auto !important;
      display: block !important;

      .carouselItemView {
        margin: 0 auto;
        max-width: 600px;

        @include respond-to(lg) {
          max-width: 700px;
        }

        @include respond-to(xl) {
          max-width: 800px;
        }
      }
    }
  }
}
</style>
