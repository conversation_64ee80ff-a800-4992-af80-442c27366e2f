<!--
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-11-04 17:50:36
-->

<template>
  <footer class="layout-footer">
    <div class="footer-main">
      <div class="footer-view">
        <div class="item-box">
          <div class="title">{{ $t("menus.aboutUs") }}</div>
          <div class="content link">
            <div class="item" @click="gotoAbout('part1')">{{ $t("aboutUs.companyProfile") }}</div>

            <!-- 2025-6-1 add by zhw -->
             <div class="item" @click="gotoAbout('part1_1')">{{ $t("aboutUs.threeAreasTitle") }}</div>
             <div class="item" @click="gotoAbout('part1_2')">{{ $t("aboutUs.scienceTitle") }}</div>

            <div class="item" @click="gotoAbout('part2')">{{ $t("aboutUs.visionAndCulture") }}</div>
            <div class="item" @click="gotoAbout('part3')">{{ $t("aboutUs.companyCulture") }}</div>
            <!-- 2025-6-1 by zhw -->
            <!-- <div class="item" @click="gotoAbout('part4')">{{ $t("aboutUs.chairmanMessage") }}</div> -->
            <div class="item" @click="gotoAbout('part7')">{{ $t("aboutUs.organizationalStructure") }}</div>
            <div class="item" @click="gotoAbout('part7_1')">{{ $t("aboutUs.officeEnvTitle") }}</div>
          </div>
        </div>
      </div>
      <div class="footer-view">
        <div class="item-box">
          <div class="title">{{ $t("menus.businessSegment") }}</div>
          <div class="content link">
            <!-- 2025-6-1 by zhw -->
            <!-- <div class="item title-item" @click="gotoBusinessDetails()">
              {{ $t("home.scientificResearchSection") }}
            </div> -->
            <div class="item title-item" @click="gotoBusinessDetails()">
              {{ $t("home.scienceTitle") }}
            </div>
            <div class="item title-item" @click="gotoIndustrialBaseDetails()">
              {{ $t("home.industrialBase") }}
            </div>
            <div class="item title-item" @click="gotoEnergyValleyDetails()">
              {{ $t("home.energyValley") }}
            </div>
            <!-- 2025-6-1 by zhw -->
            <div class="item" @click="gotoBusinessDetails()">生态与可持续发展</div>
            <div class="item" @click="gotoBusinessDetails()">生物医疗</div>
            <div class="item" @click="gotoBusinessDetails()">能源与安全</div>
            <div class="item title-item" @click="gotoNews()">
              {{ $t("menus.news") }}
            </div>
            <div class="item title-item" @click="gotoTangka()">
              {{ $t("menus.tangKa") }}
            </div>
          </div>
        </div>
      </div>
      <div class="footer-view">
        <div class="item-box">
          <div class="title">{{ $t("contactUs.title") }}</div>
          <div class="content">
            <div class="item">
              <el-icon><MapLocation /></el-icon><span class="address">{{ $t("contactUs.address1") }}</span>
            </div>
            <div class="item">
              <el-icon><Phone /></el-icon> <span>+86 0755-27211126</span>
            </div>
            <div class="item">
              <el-icon><Message /></el-icon> <span><EMAIL></span>
            </div>
          </div>
        </div>
      </div>
      <div class="footer-view">
        <div class="item-box">
          <div class="logo">
            <el-image :src="logoImg" fit="cover" lazy />
          </div>
        </div>
      </div>
      <div class="footer-view">
        <div class="item-box-qr">
          <div class="QR-code">
            <el-image class="img" :src="wechatQRcodeImg" fit="cover" lazy />
          </div>
          <p class="desc">{{ $t("footer.groupName") }}</p>
          <p class="desc">{{ $t("footer.wechatQRcode") }}</p>
        </div>
      </div>
    </div>
    <div class="footer-tip">© 2024 by sinofortune.co. All rights reserved!</div>
  </footer>
</template>

<script setup>
import { useRouter } from "vue-router"
import logoImg from "@/assets/images/logo.png"
import wechatQRcodeImg from "@/assets/images/wechat-QRcode-img.jpg"
import BusinessSegmentList from "@/views/business-segment/business-segment-list.js"

import { reactive, watch, ref } from "vue"
import { i18n } from "@/i18n"

const businessText = reactive(BusinessSegmentList)
const router = useRouter()

const lang = ref(i18n.global.locale)
watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  }
)

const gotoAbout = (part) => {
  router.push({ name: "About", query: { part } })
}

const gotoLeadershipTeam = () => {
  router.push({ path: "/leadership-team", query: { part: "part1" } })
}

const gotoBusinessDetails = () => {
  router.push({ path: "/business-segment/business-details" })
}

const gotoIndustrialBaseDetails = () => {
  router.push({ path: "/business-segment/industrial-base-details" })
}

const gotoEnergyValleyDetails = () => {
  router.push({ path: "/business-segment/energy-valley-details" })
}
const gotoNews = () => {
  router.push({ path: "/news" })
}
const gotoTangka = () => {
  router.push({ path: "/tangka" })
}
</script>

<style lang="scss" scoped>
.layout-footer {
  background: #1c2a77;

  .footer-main {
    @include responsive-container;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    // 响应式最小高度
    min-height: 200px;

    @include respond-to(md) {
      min-height: 250px;
    }

    @include respond-to(lg) {
      min-height: 300px;
    }

    @include respond-to(fhd) {
      min-height: 350px;
    }

    @include respond-to(qhd) {
      min-height: 400px;
    }

    @include respond-to(uhd) {
      min-height: 500px;
    }

    // 响应式布局和间距
    flex-direction: column;
    gap: 2rem;
    padding: 3rem 0;

    @include respond-to(md) {
      flex-direction: row;
      flex-wrap: wrap;
      gap: 3rem;
      padding: 4rem 0;
    }

    @include respond-to(lg) {
      flex-wrap: nowrap;
      gap: 4rem;
      padding: 5rem 0;
    }

    @include respond-to(fhd) {
      gap: 5rem;
      padding: 6rem 0;
    }

    @include respond-to(qhd) {
      gap: 6rem;
      padding: 7rem 0;
    }

    @include respond-to(uhd) {
      gap: 8rem;
      padding: 8rem 0;
    }

    .footer-view {
      display: flex;
      justify-content: space-between;
      flex: 1;

      // 移动端单列布局
      flex-direction: column;
      gap: 2rem;

      @include respond-to(md) {
        flex-direction: row;
        gap: 3rem;
      }

      @include respond-to(lg) {
        gap: 4rem;
      }

      @include respond-to(fhd) {
        gap: 5rem;
      }
    }

    .item-box {
      display: flex;
      flex-direction: column;
      text-align: center;

      // 响应式间距
      gap: 1rem;

      @include respond-to(md) {
        text-align: left;
        gap: 1.25rem;
      }

      @include respond-to(lg) {
        gap: 1.5rem;
      }

      @include respond-to(fhd) {
        gap: 2rem;
      }

      @include respond-to(qhd) {
        gap: 2.5rem;
      }

      @include respond-to(uhd) {
        gap: 3rem;
      }

      .title {
        color: #ffffff;
        font-weight: 700;

        // 响应式字体大小
        font-size: 14px;

        @include respond-to(sm) {
          font-size: 15px;
        }

        @include respond-to(md) {
          font-size: 16px;
        }

        @include respond-to(lg) {
          font-size: 17px;
        }

        @include respond-to(xl) {
          font-size: 18px;
        }

        @include respond-to(fhd) {
          font-size: 20px;
        }

        @include respond-to(qhd) {
          font-size: 22px;
        }

        @include respond-to(uhd) {
          font-size: 26px;
        }
        letter-spacing: 1px;
      }

      .content {
        display: flex;
        flex-direction: column;
        gap: 10px;

        .item {
          display: flex;
          align-items: center;
          max-width: 360px;
          font-size: 16px;
          color: #f0f0f0;
          width: fit-content;

          span {
            margin-left: 10px;
          }
        }
      }

      .link {
        .item {
          cursor: pointer;

          &:hover:not(.title-item) {
            text-decoration: underline;
          }
        }

        .title-item {
          font-weight: 600;
          font-size: 18px;
        }
      }
    }

    .item-box-qr {
      display: flex;
      flex-direction: column;
      width: 190px;

      .desc {
        font-size: 16px;
        color: #f0f0f0;
        text-align: center;
        font-weight: 600;
        letter-spacing: 1px;
      }
    }
  }

  .footer-tip {
    color: #ffffff;
    text-align: center;
    padding: 10px 0;
  }
}

  // 响应式优化的Footer样式已经集成在上面的主要样式中
  // 移除了所有旧的媒体查询，使用新的响应式系统
</style>
