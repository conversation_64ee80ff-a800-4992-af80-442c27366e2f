<!--
 * 响应式测试页面
 * 用于测试和验证响应式设计效果
 -->

<template>
  <div class="responsive-test">
    <ResponsiveContainer type="container">
      <h1 class="text-responsive-xl">响应式设计测试页面</h1>
      
      <!-- 设备信息显示 -->
      <div class="device-info spacing-md">
        <h2 class="text-responsive-lg">当前设备信息</h2>
        <div class="info-grid">
          <div class="info-item">
            <strong>窗口宽度:</strong> {{ windowWidth }}px
          </div>
          <div class="info-item">
            <strong>窗口高度:</strong> {{ windowHeight }}px
          </div>
          <div class="info-item">
            <strong>设备类型:</strong> {{ deviceType }}
          </div>
          <div class="info-item">
            <strong>当前断点:</strong> {{ currentBreakpoint }}
          </div>
        </div>
      </div>
      
      <!-- 断点测试 -->
      <div class="breakpoint-test spacing-md">
        <h2 class="text-responsive-lg">断点测试</h2>
        <div class="breakpoint-indicators">
          <div class="indicator" :class="{ active: isXs }">XS (< 576px)</div>
          <div class="indicator" :class="{ active: isSm }">SM (576px - 767px)</div>
          <div class="indicator" :class="{ active: isMd }">MD (768px - 991px)</div>
          <div class="indicator" :class="{ active: isLg }">LG (992px - 1199px)</div>
          <div class="indicator" :class="{ active: isXl }">XL (1200px - 1399px)</div>
          <div class="indicator" :class="{ active: isXxl }">XXL (≥ 1400px)</div>
        </div>
      </div>
      
      <!-- 响应式网格测试 -->
      <div class="grid-test spacing-md">
        <h2 class="text-responsive-lg">响应式网格测试</h2>
        <div class="responsive-grid">
          <div class="grid-item" v-for="i in 6" :key="i">
            <div class="card">
              <h3>卡片 {{ i }}</h3>
              <p>这是一个响应式卡片，会根据屏幕大小调整布局。</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 响应式文本测试 -->
      <div class="text-test spacing-md">
        <h2 class="text-responsive-lg">响应式文本测试</h2>
        <div class="text-samples">
          <p class="text-responsive-xl">超大标题文本 (响应式)</p>
          <p class="text-responsive-lg">大标题文本 (响应式)</p>
          <p class="text-responsive">普通文本 (响应式)</p>
          <p class="text-sm">小文本 (固定)</p>
        </div>
      </div>
      
      <!-- 响应式显示控制测试 -->
      <div class="visibility-test spacing-md">
        <h2 class="text-responsive-lg">响应式显示控制测试</h2>
        <div class="visibility-items">
          <div class="hide-mobile">桌面端显示</div>
          <div class="hide-desktop">移动端显示</div>
          <div class="show-mobile-only">仅移动端显示</div>
          <div class="show-desktop-only">仅桌面端显示</div>
        </div>
      </div>
      
      <!-- 响应式容器测试 -->
      <div class="container-test spacing-md">
        <h2 class="text-responsive-lg">响应式容器测试</h2>
        <ResponsiveContainer 
          type="section" 
          custom-class="test-container"
          :padding="{ mobile: '1rem', tablet: '2rem', desktop: '3rem' }"
        >
          <div class="container-content">
            <p>这是一个响应式容器，具有不同的内边距设置。</p>
            <p>移动端: 1rem, 平板: 2rem, 桌面: 3rem</p>
          </div>
        </ResponsiveContainer>
      </div>
      
      <!-- 响应式值测试 -->
      <div class="value-test spacing-md">
        <h2 class="text-responsive-lg">响应式值测试</h2>
        <div class="value-demo" :style="{ fontSize: responsiveFontSize }">
          当前字体大小: {{ responsiveFontSize }}
        </div>
        <div class="value-demo" :style="{ padding: responsivePadding }">
          当前内边距: {{ responsivePadding }}
        </div>
      </div>
    </ResponsiveContainer>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive'
import ResponsiveContainer from '@/components/ResponsiveContainer/index.vue'

const {
  windowWidth,
  windowHeight,
  deviceType,
  currentBreakpoint,
  isXs,
  isSm,
  isMd,
  isLg,
  isXl,
  isXxl,
  getResponsiveFontSize,
  getResponsiveSpacing
} = useResponsive()

// 响应式值测试
const responsiveFontSize = useResponsiveValue({
  mobile: '14px',
  tablet: '16px',
  desktop: '18px'
})

const responsivePadding = useResponsiveValue({
  mobile: '8px',
  tablet: '16px',
  desktop: '24px'
})
</script>

<style lang="scss" scoped>
.responsive-test {
  min-height: 100vh;
  background: #f5f5f5;
  
  .device-info {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-top: 1rem;
      
      .info-item {
        padding: 0.5rem;
        background: #f8f9fa;
        border-radius: 4px;
      }
    }
  }
  
  .breakpoint-test {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    .breakpoint-indicators {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      margin-top: 1rem;
      
      .indicator {
        padding: 0.5rem 1rem;
        border: 2px solid #e9ecef;
        border-radius: 4px;
        background: #f8f9fa;
        transition: all 0.3s ease;
        
        &.active {
          border-color: #007bff;
          background: #007bff;
          color: white;
        }
      }
    }
  }
  
  .grid-test {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    .responsive-grid {
      @include responsive-grid(1, 2, 3);
      margin-top: 1rem;
      
      .grid-item {
        .card {
          @extend .responsive-card;
          
          h3 {
            margin: 0 0 0.5rem 0;
            color: #333;
          }
          
          p {
            margin: 0;
            color: #666;
          }
        }
      }
    }
  }
  
  .text-test {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    .text-samples {
      margin-top: 1rem;
      
      p {
        margin: 0.5rem 0;
        padding: 0.5rem;
        background: #f8f9fa;
        border-radius: 4px;
      }
    }
  }
  
  .visibility-test {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    .visibility-items {
      margin-top: 1rem;
      
      > div {
        margin: 0.5rem 0;
        padding: 0.5rem;
        background: #e3f2fd;
        border-radius: 4px;
        border-left: 4px solid #2196f3;
      }
    }
  }
  
  .container-test {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    .test-container {
      background: #f0f8ff;
      border: 2px dashed #007bff;
      border-radius: 4px;
      margin-top: 1rem;
      
      .container-content {
        text-align: center;
        
        p {
          margin: 0.5rem 0;
        }
      }
    }
  }
  
  .value-test {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    .value-demo {
      margin: 1rem 0;
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 4px;
      text-align: center;
      transition: all 0.3s ease;
    }
  }
}
</style>
