/*
 * @Author: <PERSON>
 * @Date: 2024-04-02 17:52:04
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-05-07 19:28:17
 */

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
  theme: {
    // 自定义响应式断点，与项目设计系统保持一致
    screens: {
      'xs': '480px',    // 超小屏幕 (手机)
      'sm': '576px',    // 小屏幕 (大手机)
      'md': '768px',    // 中等屏幕 (平板)
      'lg': '992px',    // 大屏幕 (小桌面)
      'xl': '1200px',   // 超大屏幕 (桌面)
      '2xl': '1400px',  // 超超大屏幕 (大桌面)
      '3xl': '1600px',  // 超宽屏
    },
    extend: {
      // 自定义容器最大宽度
      maxWidth: {
        'container': '1200px',
        'container-lg': '1400px',
        'container-xl': '1600px',
      },
      // 自定义间距
      spacing: {
        '18': '4.5rem',   // 72px
        '88': '22rem',    // 352px
        '128': '32rem',   // 512px
      },
      // 自定义字体大小 - 响应式字体
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
        '5xl': ['3rem', { lineHeight: '1' }],
        '6xl': ['3.75rem', { lineHeight: '1' }],
        '7xl': ['4.5rem', { lineHeight: '1' }],
        '8xl': ['6rem', { lineHeight: '1' }],
        '9xl': ['8rem', { lineHeight: '1' }],
        // 移动端专用字体大小
        'mobile-sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'mobile-base': ['1rem', { lineHeight: '1.5rem' }],
        'mobile-lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'mobile-xl': ['1.25rem', { lineHeight: '1.75rem' }],
        'mobile-2xl': ['1.5rem', { lineHeight: '2rem' }],
        'mobile-3xl': ['1.875rem', { lineHeight: '2.25rem' }],
      },
      // 自定义颜色
      colors: {
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
      },
      // 自定义动画
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    }
  },
  plugins: [
    // 添加自定义工具类插件
    function({ addUtilities, theme }) {
      const newUtilities = {
        // 响应式容器
        '.container-responsive': {
          width: '100%',
          marginLeft: 'auto',
          marginRight: 'auto',
          paddingLeft: '1rem',
          paddingRight: '1rem',
          '@screen sm': {
            paddingLeft: '1.5rem',
            paddingRight: '1.5rem',
          },
          '@screen md': {
            paddingLeft: '2rem',
            paddingRight: '2rem',
          },
          '@screen lg': {
            maxWidth: theme('maxWidth.container'),
          },
          '@screen xl': {
            maxWidth: theme('maxWidth.container-lg'),
          },
          '@screen 2xl': {
            maxWidth: theme('maxWidth.container-xl'),
          },
        },
        // 响应式文本
        '.text-responsive': {
          fontSize: theme('fontSize.mobile-base')[0],
          lineHeight: theme('fontSize.mobile-base')[1].lineHeight,
          '@screen sm': {
            fontSize: theme('fontSize.base')[0],
            lineHeight: theme('fontSize.base')[1].lineHeight,
          },
          '@screen md': {
            fontSize: theme('fontSize.lg')[0],
            lineHeight: theme('fontSize.lg')[1].lineHeight,
          },
        },
        '.text-responsive-lg': {
          fontSize: theme('fontSize.mobile-lg')[0],
          lineHeight: theme('fontSize.mobile-lg')[1].lineHeight,
          '@screen sm': {
            fontSize: theme('fontSize.xl')[0],
            lineHeight: theme('fontSize.xl')[1].lineHeight,
          },
          '@screen md': {
            fontSize: theme('fontSize.2xl')[0],
            lineHeight: theme('fontSize.2xl')[1].lineHeight,
          },
        },
        '.text-responsive-xl': {
          fontSize: theme('fontSize.mobile-xl')[0],
          lineHeight: theme('fontSize.mobile-xl')[1].lineHeight,
          '@screen sm': {
            fontSize: theme('fontSize.2xl')[0],
            lineHeight: theme('fontSize.2xl')[1].lineHeight,
          },
          '@screen md': {
            fontSize: theme('fontSize.3xl')[0],
            lineHeight: theme('fontSize.3xl')[1].lineHeight,
          },
          '@screen lg': {
            fontSize: theme('fontSize.4xl')[0],
            lineHeight: theme('fontSize.4xl')[1].lineHeight,
          },
        },
        // 响应式间距
        '.spacing-responsive': {
          padding: '1rem',
          '@screen sm': {
            padding: '1.5rem',
          },
          '@screen md': {
            padding: '2rem',
          },
          '@screen lg': {
            padding: '3rem',
          },
        },
      }
      addUtilities(newUtilities)
    }
  ]
}
