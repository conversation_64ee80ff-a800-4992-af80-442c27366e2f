<!--
 * @Author: <PERSON>
 * @Date: 2024-04-01 14:08:43
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-11-13 11:30:05
-->

<template>
  <div class="great-health-main">
    <div class="content">
      <div class="video-view">
        <div class="txt-main">
          <div class="txt">{{ langMap[lang].desc1 }}</div>
          <div class="txt">{{ langMap[lang].desc2 }}</div>
          <div class="txt-tip">{{ langMap[lang].desc3 }}</div>
        </div>
        <video class="video" :src="greatHealthVideo" controls controlsList="nodownload" preload="metadata">
          您的浏览器不支持视频标签。
        </video>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from "vue"

import { i18n } from "@/i18n"

const lang = ref(i18n.global.locale)

const greatHealthVideo = ref("/resources/video/greatHealthVideo.mp4")

const langMap = ref({
  zh: {
    desc1: "中兆国际集团",
    desc2: "大健康板块隆重上市",
    desc3: "各界大咖明星齐祝福、共庆辉煌时刻"
  },
  en: {
    desc1: "Zhongzhao International Group",
    desc2: "The Comprehensive Health Segment is Officially Launched",
    desc3: "Wishes from Celebrities to Mark this Moment"
  },
  zh_hk: {
    desc1: "中兆國際集團",
    desc2: "大健康板塊隆重上市",
    desc3: "各界大咖明星齊祝福、共慶輝煌時刻"
  }
})

watch(
  () => i18n.global.locale,
  (newVal) => {
    lang.value = newVal ? newVal : "zh"
  }
)
</script>

<style lang="scss" scoped>
.great-health-main {
  background: #ffffff;

  .content {
    background: url("@/assets/home/<USER>") no-repeat center center;
    background-size: cover;

    display: flex;
    justify-content: center;

    .video-view {
      @extend .responsive-container;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      // 响应式内边距
      padding-top: 3rem;
      padding-bottom: 4rem;

      @include respond-to(md) {
        padding-top: 4rem;
        padding-bottom: 6rem;
      }

      @include respond-to(lg) {
        padding-top: 5rem;
        padding-bottom: 8rem;
      }

      @include respond-to(xl) {
        padding-top: 6rem;
        padding-bottom: 10rem;
      }

      @include respond-to(fhd) {
        padding-top: 7rem;
        padding-bottom: 12rem;
      }

      @include respond-to(qhd) {
        padding-top: 8rem;
        padding-bottom: 14rem;
      }

      @include respond-to(uhd) {
        padding-top: 10rem;
        padding-bottom: 18rem;
      }

      .txt-main {
        width: 100%;
        text-align: center;
        margin-bottom: 50px;

        .txt {
          font-family: SourceHanSansCN, SourceHanSansCN;
          color: #ffffff;
          font-weight: 700;
          text-shadow: 2px 2px 12px rgba(0, 0, 0, 0.3);

          // 响应式字体大小和字间距
          font-size: 20px;
          letter-spacing: 1px;
          margin-bottom: 0.5rem;

          @include respond-to(sm) {
            font-size: 24px;
            letter-spacing: 1.5px;
          }

          @include respond-to(md) {
            font-size: 28px;
            letter-spacing: 2px;
            margin-bottom: 0.75rem;
          }

          @include respond-to(lg) {
            font-size: 32px;
            letter-spacing: 2.5px;
          }

          @include respond-to(xl) {
            font-size: 36px;
            letter-spacing: 3px;
            margin-bottom: 1rem;
          }

          @include respond-to(fhd) {
            font-size: 40px;
            letter-spacing: 3px;
          }

          @include respond-to(qhd) {
            font-size: 44px;
            letter-spacing: 3.5px;
          }

          @include respond-to(uhd) {
            font-size: 52px;
            letter-spacing: 4px;
          }
        }

        .txt-tip {
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: 500;
          font-size: 28px;
          color: #90d6ff;
          line-height: 50px;
          letter-spacing: 2px;
          text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
          margin-top: 10px;
        }
      }

      .video {
        width: 800px;
        height: 100%;
      }
    }
  }
}

// 响应式样式已经集成在主要样式中，使用新的响应式系统
</style>
