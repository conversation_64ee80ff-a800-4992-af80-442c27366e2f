/*
 * 响应式工具类
 * 提供统一的响应式设计工具和mixins
 */

@import "./variables.css";

// 导入响应式常量
$breakpoints: (
  xs: 480px,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px,
  xxxl: 1600px
);

// 媒体查询 mixin
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}.";
  }
}

// 范围媒体查询 mixin
@mixin respond-between($min, $max) {
  @if map-has-key($breakpoints, $min) and map-has-key($breakpoints, $max) {
    @media (min-width: map-get($breakpoints, $min)) and (max-width: map-get($breakpoints, $max) - 1px) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$min} or #{$max}.";
  }
}

// 最大宽度媒体查询 mixin
@mixin respond-down($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (max-width: map-get($breakpoints, $breakpoint) - 1px) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}.";
  }
}

// 响应式字体大小 mixin
@mixin responsive-font-size($mobile, $tablet: null, $desktop: null) {
  font-size: $mobile;
  
  @if $tablet {
    @include respond-to(md) {
      font-size: $tablet;
    }
  }
  
  @if $desktop {
    @include respond-to(lg) {
      font-size: $desktop;
    }
  }
}

// 响应式间距 mixin
@mixin responsive-spacing($property, $mobile, $tablet: null, $desktop: null) {
  #{$property}: $mobile;
  
  @if $tablet {
    @include respond-to(md) {
      #{$property}: $tablet;
    }
  }
  
  @if $desktop {
    @include respond-to(lg) {
      #{$property}: $desktop;
    }
  }
}

// 响应式容器 mixin
@mixin responsive-container($max-width: 1200px) {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
  
  @include respond-to(sm) {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  
  @include respond-to(md) {
    padding-left: 2rem;
    padding-right: 2rem;
  }
  
  @include respond-to(lg) {
    max-width: $max-width;
  }
}

// 响应式网格 mixin
@mixin responsive-grid($mobile-cols: 1, $tablet-cols: 2, $desktop-cols: 3) {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat($mobile-cols, 1fr);
  
  @include respond-to(md) {
    grid-template-columns: repeat($tablet-cols, 1fr);
    gap: 1.5rem;
  }
  
  @include respond-to(lg) {
    grid-template-columns: repeat($desktop-cols, 1fr);
    gap: 2rem;
  }
}

// 响应式flexbox mixin
@mixin responsive-flex($mobile-direction: column, $desktop-direction: row) {
  display: flex;
  flex-direction: $mobile-direction;
  gap: 1rem;
  
  @include respond-to(lg) {
    flex-direction: $desktop-direction;
    gap: 2rem;
  }
}

// 隐藏/显示工具类
.hide-mobile {
  @include respond-down(lg) {
    display: none !important;
  }
}

.hide-desktop {
  @include respond-to(lg) {
    display: none !important;
  }
}

.show-mobile-only {
  display: block;
  
  @include respond-to(lg) {
    display: none !important;
  }
}

.show-desktop-only {
  display: none;
  
  @include respond-to(lg) {
    display: block !important;
  }
}

// 响应式文本对齐
.text-center-mobile {
  text-align: center;
  
  @include respond-to(lg) {
    text-align: left;
  }
}

// 响应式宽度
.full-width-mobile {
  width: 100%;
  
  @include respond-to(lg) {
    width: auto;
  }
}

// 响应式高度
.responsive-height {
  height: 300px;
  
  @include respond-to(md) {
    height: 400px;
  }
  
  @include respond-to(lg) {
    height: 500px;
  }
}

// 响应式图片
.responsive-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  
  @include respond-to(lg) {
    max-width: 100%;
  }
}

// 响应式视频
.responsive-video {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; // 16:9 aspect ratio
  
  iframe,
  video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

// 响应式卡片
.responsive-card {
  @include responsive-spacing(padding, 1rem, 1.5rem, 2rem);
  @include responsive-spacing(margin-bottom, 1rem, 1.5rem, 2rem);
  
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background: white;
}

// 响应式按钮
.responsive-button {
  @include responsive-spacing(padding, 0.75rem 1rem, 0.875rem 1.25rem, 1rem 1.5rem);
  @include responsive-font-size(0.875rem, 1rem, 1.125rem);
  
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
}

// 响应式导航
.responsive-nav {
  @include respond-down(lg) {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    
    &.hidden {
      display: none;
    }
  }
  
  @include respond-to(lg) {
    position: static;
    background: transparent;
  }
}

// 响应式表格
.responsive-table {
  width: 100%;
  overflow-x: auto;
  
  table {
    width: 100%;
    min-width: 600px;
    
    @include respond-to(lg) {
      min-width: auto;
    }
  }
}

// 响应式间距工具类
@each $size, $value in (xs: 0.5rem, sm: 1rem, md: 1.5rem, lg: 2rem, xl: 3rem) {
  .spacing-#{$size} {
    @include responsive-spacing(padding, $value);
  }
  
  .spacing-x-#{$size} {
    @include responsive-spacing(padding-left, $value);
    @include responsive-spacing(padding-right, $value);
  }
  
  .spacing-y-#{$size} {
    @include responsive-spacing(padding-top, $value);
    @include responsive-spacing(padding-bottom, $value);
  }
}
